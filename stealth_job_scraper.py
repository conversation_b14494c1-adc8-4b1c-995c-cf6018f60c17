#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import random
import json

def setup_stealth_driver():
    """Set up Chrome driver with stealth options to avoid detection"""
    options = webdriver.ChromeOptions()
    
    # Basic stealth options
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # Additional anti-detection measures
    options.add_argument("--disable-web-security")
    options.add_argument("--disable-features=VizDisplayCompositor")
    options.add_argument("--disable-extensions")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    
    # Set a realistic user agent
    options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    
    # Execute script to hide webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def human_like_delay():
    """Add random human-like delays"""
    time.sleep(random.uniform(2, 5))

def stealth_google_search():
    """
    Stealth Google search with anti-detection measures
    """
    driver = None
    jobs = []
    
    try:
        print("🥷 Starting STEALTH Google Job Search")
        print("=" * 50)
        
        driver = setup_stealth_driver()
        print("✅ Stealth Chrome driver created!")
        
        # First, go to Google homepage to establish session
        print("🌐 Visiting Google homepage first...")
        driver.get("https://www.google.com")
        human_like_delay()
        
        # Accept cookies if prompted (common anti-bot measure)
        try:
            accept_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'I agree')]"))
            )
            accept_button.click()
            print("✅ Accepted cookies")
            human_like_delay()
        except:
            print("ℹ️ No cookie banner found")
        
        # Now navigate to the search URL
        search_url = "https://www.google.com/search?q=%22Full%20Stack%20Engineer%22+site:greenhouse.io+remote&tbs=qdr:d"
        
        print("🔍 Navigating to search results...")
        driver.get(search_url)
        human_like_delay()
        
        # Check if we got blocked
        page_text = driver.find_element(By.TAG_NAME, "body").text.lower()
        if "unusual traffic" in page_text or "captcha" in page_text or "robot" in page_text:
            print("🚫 Still got blocked. Let's try alternative approach...")
            return try_alternative_search(driver)
        
        print("✅ Successfully loaded search results!")
        
        # Extract results
        jobs = extract_job_results(driver)
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during stealth search: {e}")
        return []
        
    finally:
        if driver:
            try:
                print("🕐 Keeping browser open for 20 seconds...")
                time.sleep(20)
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def try_alternative_search(driver):
    """
    Alternative approach: Use DuckDuckGo or Bing instead of Google
    """
    jobs = []
    
    try:
        print("🦆 Trying DuckDuckGo as alternative...")
        
        # DuckDuckGo search
        duckduckgo_url = 'https://duckduckgo.com/?q="Full Stack Engineer" site:greenhouse.io remote&df=d'
        driver.get(duckduckgo_url)
        human_like_delay()
        
        # Extract results from DuckDuckGo
        search_results = driver.find_elements(By.CSS_SELECTOR, "[data-result]")
        
        if not search_results:
            # Try different selector
            search_results = driver.find_elements(By.CSS_SELECTOR, ".result")
        
        print(f"🔎 Found {len(search_results)} results on DuckDuckGo")
        
        for i, result in enumerate(search_results[:10]):
            try:
                # Extract title and link from DuckDuckGo
                title_element = result.find_element(By.CSS_SELECTOR, "h2 a, .result__title a")
                title = title_element.text.strip()
                link = title_element.get_attribute("href")
                
                # Extract description
                try:
                    desc_element = result.find_element(By.CSS_SELECTOR, ".result__snippet, .result__body")
                    description = desc_element.text.strip()
                except:
                    description = "No description available"
                
                if "greenhouse.io" in link.lower():
                    job = {
                        'title': title,
                        'link': link,
                        'description': description,
                        'source': 'DuckDuckGo Search'
                    }
                    jobs.append(job)
                    
                    print(f"\n📌 Job {len(jobs)}:")
                    print(f"   Title: {title}")
                    print(f"   Link: {link}")
                
            except Exception as e:
                print(f"⚠️ Error extracting DuckDuckGo result {i+1}: {e}")
                continue
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error with alternative search: {e}")
        return []

def extract_job_results(driver):
    """Extract job results from Google search page"""
    jobs = []
    
    try:
        # Try multiple selectors for search results
        search_results = []
        selectors_to_try = [
            "div.g",
            "div[data-ved]",
            ".g",
            "div.yuRUbf",
            "div.tF2Cxc",
        ]
        
        for selector in selectors_to_try:
            try:
                results = driver.find_elements(By.CSS_SELECTOR, selector)
                if results:
                    search_results = results
                    print(f"✅ Found {len(search_results)} search results")
                    break
            except:
                continue
        
        if not search_results:
            print("⚠️ No search results found with standard selectors")
            return []
        
        # Extract job information
        for i, result in enumerate(search_results[:15]):
            try:
                title = ""
                link = ""
                description = ""
                
                # Try to find title
                title_selectors = ["h3", ".LC20lb", ".DKV0Md", "h3 a"]
                for selector in title_selectors:
                    try:
                        title_element = result.find_element(By.CSS_SELECTOR, selector)
                        title = title_element.text.strip()
                        if title:
                            break
                    except:
                        continue
                
                # Try to find link
                link_selectors = ["a", "h3 a", ".yuRUbf a"]
                for selector in link_selectors:
                    try:
                        link_element = result.find_element(By.CSS_SELECTOR, selector)
                        link = link_element.get_attribute("href")
                        if link and link.startswith("http"):
                            break
                    except:
                        continue
                
                # Try to find description
                desc_selectors = [".VwiC3b", ".s3v9rd", ".st", "span[data-st]", ".IsZvec"]
                for selector in desc_selectors:
                    try:
                        desc_element = result.find_element(By.CSS_SELECTOR, selector)
                        description = desc_element.text.strip()
                        if description:
                            break
                    except:
                        continue
                
                if not description:
                    description = "No description available"
                
                # Only include greenhouse.io results
                if link and "greenhouse.io" in link.lower():
                    job = {
                        'title': title,
                        'link': link,
                        'description': description,
                        'source': 'Google Search (Stealth)'
                    }
                    jobs.append(job)
                    
                    print(f"\n📌 Job {len(jobs)}:")
                    print(f"   Title: {title}")
                    print(f"   Link: {link}")
                    print(f"   Description: {description[:100]}...")
                
            except Exception as e:
                print(f"⚠️ Error extracting result {i+1}: {e}")
                continue
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error extracting results: {e}")
        return []

def save_jobs_to_file(jobs, filename="stealth_jobs.json"):
    """Save jobs to files"""
    try:
        # Save as JSON
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(jobs, f, indent=2, ensure_ascii=False)
        
        # Save as readable text
        txt_filename = filename.replace('.json', '.txt')
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write("FULL STACK ENGINEER JOBS (STEALTH SCRAPING)\n")
            f.write("=" * 50 + "\n\n")
            
            for i, job in enumerate(jobs, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"Title: {job['title']}\n")
                f.write(f"Link: {job['link']}\n")
                f.write(f"Description: {job['description']}\n")
                f.write(f"Source: {job['source']}\n")
                f.write("-" * 40 + "\n\n")
        
        print(f"💾 Jobs saved to: {filename} and {txt_filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving jobs: {e}")
        return False

def main():
    """Main function"""
    print("=" * 60)
    print("🥷 STEALTH JOB SCRAPER - ANTI-CAPTCHA VERSION")
    print("=" * 60)
    print("Using techniques to avoid detection:")
    print("• 🎭 Stealth browser settings")
    print("• 🕐 Human-like delays")
    print("• 🦆 Fallback to DuckDuckGo if blocked")
    print("• 🔄 Multiple extraction methods")
    print("=" * 60)
    
    jobs = stealth_google_search()
    
    if jobs:
        print(f"\n🎉 SUCCESS! Found {len(jobs)} jobs!")
        save_jobs_to_file(jobs)
    else:
        print("\n❌ No jobs found with stealth methods")
        print("💡 You might want to try the interactive version instead")

if __name__ == "__main__":
    main()
