#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time
import json

def setup_driver():
    """Set up Chrome driver"""
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    # Keep browser visible so user can interact
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    return driver

def interactive_google_search():
    """
    Interactive Google search - opens browser and waits for user to handle CAPTCHAs
    """
    driver = None
    jobs = []
    
    try:
        print("🚀 Starting Interactive Google Job Search")
        print("=" * 50)
        
        driver = setup_driver()
        print("✅ Chrome browser opened!")
        
        # The search URL you provided
        search_url = "https://www.google.com/search?q=%22Full%20Stack%20Engineer%22+site:greenhouse.io+remote&tbs=qdr:d"
        
        print(f"🌐 Opening Google search...")
        driver.get(search_url)
        
        print("\n" + "=" * 60)
        print("🤖 CAPTCHA DETECTION NOTICE")
        print("=" * 60)
        print("If Google shows a CAPTCHA or 'unusual traffic' message:")
        print("1. ✅ Complete the CAPTCHA in the browser window")
        print("2. ✅ Wait for search results to load")
        print("3. ✅ Press ENTER here when results are visible")
        print("=" * 60)
        
        # Wait for user to handle any CAPTCHAs
        input("\n⏳ Press ENTER when you can see the Google search results...")
        
        print("\n🔍 Now extracting job listings...")
        
        # Give page time to fully load
        time.sleep(3)
        
        # Try multiple selectors for search results
        search_results = []
        selectors_to_try = [
            "div.g",                    # Traditional search results
            "div[data-ved]",           # Alternative selector
            ".g",                      # Simple class selector
            "div.yuRUbf",             # New Google layout
            "div.tF2Cxc",             # Another Google layout
        ]
        
        for selector in selectors_to_try:
            try:
                results = driver.find_elements(By.CSS_SELECTOR, selector)
                if results:
                    search_results = results
                    print(f"✅ Found {len(search_results)} search results using selector: {selector}")
                    break
            except:
                continue
        
        if not search_results:
            print("⚠️ No search results found. Let me check what's on the page...")
            print(f"Page title: {driver.title}")
            
            # Try to find any links that might be job results
            all_links = driver.find_elements(By.TAG_NAME, "a")
            greenhouse_links = [link for link in all_links if "greenhouse.io" in link.get_attribute("href") or ""]
            
            if greenhouse_links:
                print(f"🔗 Found {len(greenhouse_links)} greenhouse.io links on the page")
                search_results = greenhouse_links
            else:
                print("❌ No greenhouse.io links found")
                return []
        
        # Extract job information
        for i, result in enumerate(search_results[:15]):
            try:
                title = ""
                link = ""
                description = ""
                
                # If this is a direct link element
                if result.tag_name == "a":
                    link = result.get_attribute("href")
                    title = result.text.strip()
                else:
                    # Try to find title
                    title_selectors = ["h3", ".LC20lb", ".DKV0Md", "h3 a", ".yuRUbf h3"]
                    for selector in title_selectors:
                        try:
                            title_element = result.find_element(By.CSS_SELECTOR, selector)
                            title = title_element.text.strip()
                            if title:
                                break
                        except:
                            continue
                    
                    # Try to find link
                    link_selectors = ["a", "h3 a", ".yuRUbf a", "div a"]
                    for selector in link_selectors:
                        try:
                            link_element = result.find_element(By.CSS_SELECTOR, selector)
                            link = link_element.get_attribute("href")
                            if link and link.startswith("http"):
                                break
                        except:
                            continue
                    
                    # Try to find description
                    desc_selectors = [".VwiC3b", ".s3v9rd", ".st", "span[data-st]", ".IsZvec", ".yXK7lf"]
                    for selector in desc_selectors:
                        try:
                            desc_element = result.find_element(By.CSS_SELECTOR, selector)
                            description = desc_element.text.strip()
                            if description:
                                break
                        except:
                            continue
                
                if not description:
                    description = "No description available"
                
                # Only include greenhouse.io results
                if link and "greenhouse.io" in link.lower():
                    job = {
                        'title': title,
                        'link': link,
                        'description': description,
                        'source': 'Google Search (Past 24 Hours)'
                    }
                    jobs.append(job)
                    
                    print(f"\n📌 Job {len(jobs)}:")
                    print(f"   Title: {title}")
                    print(f"   Link: {link}")
                    print(f"   Description: {description[:150]}...")
                
            except Exception as e:
                print(f"⚠️ Error extracting result {i+1}: {e}")
                continue
        
        # Keep browser open for user to see results
        if jobs:
            print(f"\n✅ Found {len(jobs)} Full Stack Engineer jobs!")
            print("\n🕐 Keeping browser open for 30 seconds so you can see the results...")
            print("💡 You can click on any job links to view them")
            time.sleep(30)
        else:
            print("\n❌ No greenhouse.io jobs found in the results")
            print("🕐 Keeping browser open for 15 seconds so you can check manually...")
            time.sleep(15)
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return []
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def save_jobs_to_file(jobs, filename="greenhouse_jobs_24h.json"):
    """Save jobs to a JSON file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(jobs, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Jobs saved to: {filename}")
        
        # Also save as readable text file
        txt_filename = filename.replace('.json', '.txt')
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write("FULL STACK ENGINEER JOBS FROM GREENHOUSE.IO (PAST 24 HOURS)\n")
            f.write("=" * 60 + "\n\n")
            
            for i, job in enumerate(jobs, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"Title: {job['title']}\n")
                f.write(f"Link: {job['link']}\n")
                f.write(f"Description: {job['description']}\n")
                f.write(f"Source: {job['source']}\n")
                f.write("-" * 50 + "\n\n")
        
        print(f"💾 Readable version saved to: {txt_filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving jobs: {e}")
        return False

def main():
    """Main function"""
    print("=" * 70)
    print("🔍 INTERACTIVE GOOGLE JOB SCRAPER FOR GREENHOUSE.IO")
    print("🕐 SEARCHING JOBS FROM PAST 24 HOURS")
    print("=" * 70)
    
    # Search for jobs interactively
    jobs = interactive_google_search()
    
    if jobs:
        print(f"\n✅ SUCCESS! Found {len(jobs)} Full Stack Engineer jobs!")
        print("\n📋 SUMMARY:")
        print("-" * 40)
        
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job['title']}")
            print(f"   🔗 {job['link']}")
            print()
        
        # Save results
        save_jobs_to_file(jobs)
        
        print("\n🎉 Job search completed successfully!")
    else:
        print("\n❌ No jobs found. This could be because:")
        print("   • No new Full Stack Engineer jobs were posted in the last 24 hours")
        print("   • Google is blocking the search")
        print("   • The search results format has changed")

if __name__ == "__main__":
    main()
