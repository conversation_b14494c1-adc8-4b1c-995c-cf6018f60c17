from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time

# ---------- SETUP CHROME DRIVER ----------
def setup_driver():
    """Set up Chrome driver"""
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    # Keep browser visible (no headless mode)

    # Specify Chrome binary path for macOS
    options.binary_location = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

    try:
        print("🔧 Setting up ChromeDriver...")
        # Use the ChromeDriver we installed with Homebrew
        service = Service("/usr/local/bin/chromedriver")
        driver = webdriver.Chrome(service=service, options=options)
        print("✅ ChromeDriver setup successful!")
        return driver
    except Exception as e:
        print(f"❌ ChromeDriver setup failed: {e}")
        print("� Make sure Chrome browser and ChromeDriver are installed")
        raise e

# ---------- READ HTML FROM URL ----------
def read_html_from_url(driver, url):
    """Navigate to URL and read HTML content"""
    try:
        print(f"🌐 Opening: {url}")
        driver.get(url)
        time.sleep(3)  # Wait for page to load

        # Get page title
        title = driver.title
        print(f"� Page Title: {title}")

        # Get full HTML source
        html_content = driver.page_source
        print(f"📝 HTML Length: {len(html_content)} characters")

        # Print first 500 characters of HTML
        print("🔍 HTML Preview (first 500 chars):")
        print("-" * 50)
        print(html_content[:500])
        print("-" * 50)

        # You can also find specific elements
        try:
            # Example: Find all links on the page
            links = driver.find_elements(By.TAG_NAME, "a")
            print(f"🔗 Found {len(links)} links on the page")

            # Print first 5 link texts and URLs
            print("📋 First 5 links:")
            for i, link in enumerate(links[:5]):
                link_text = link.text.strip()
                link_url = link.get_attribute("href")
                print(f"  {i+1}. Text: '{link_text}' -> URL: {link_url}")

        except Exception as e:
            print(f"⚠️ Error finding links: {e}")

        return html_content

    except Exception as e:
        print(f"❌ Error reading HTML: {e}")
        return None

# ---------- MAIN FLOW ----------
def main():
    driver = None
    try:
        # Setup driver
        driver = setup_driver()

        # Test URLs - you can change these
        test_urls = [
            "https://www.google.com",
            "https://www.workatastartup.com/companies",
            # Add more URLs as needed
        ]

        for url in test_urls:
            html_content = read_html_from_url(driver, url)
            if html_content:
                print(f"✅ Successfully read HTML from {url}")
            else:
                print(f"❌ Failed to read HTML from {url}")

            # Wait a bit between requests
            time.sleep(2)

        # Keep browser open for 15 seconds so you can see it
        print("🕐 Keeping browser open for 15 seconds...")
        time.sleep(15)

    except Exception as e:
        print(f"❌ Error in main: {e}")
    finally:
        if driver:
            driver.quit()
            print("🧹 Browser closed.")

if __name__ == "__main__":
    main()
