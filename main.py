from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time

# ---------- CONFIGURATION ----------
EMAIL = "semeon-light"
PASSWORD = "Lol!pop320875"
LISTING_URL = "https://www.workatastartup.com/companies?layout=list-compact&sortBy=created_desc"

# ---------- SETUP CHROME DRIVER ----------
options = webdriver.ChromeOptions()
# options.add_argument("--headless")  # Visible browser
options.add_argument("--start-maximized")
driver = webdriver.Chrome(options=options)

# ---------- LOGIN ----------
def login():
    driver.get("https://www.workatastartup.com/login")
    time.sleep(3)
    email_input = driver.find_element(By.NAME, "email")
    password_input = driver.find_element(By.NAME, "password")
    email_input.send_keys(EMAIL)
    password_input.send_keys(PASSWORD)
    password_input.send_keys(Keys.RETURN)
    print("🔐 Logged in.")
    time.sleep(5)

# ---------- SCRAPE JOB LINKS ----------
def scrape_job_links(limit=5):
    driver.get(LISTING_URL)
    time.sleep(5)

    job_links = []
    cards = driver.find_elements(By.CSS_SELECTOR, "a.company-row")  # Each company row is a link
    for card in cards[:limit]:  # Limit to N companies to avoid spam
        link = card.get_attribute("href")
        if link:
            job_links.append(link)
    print(f"🔎 Found {len(job_links)} job links.")
    return job_links

# ---------- APPLY TO JOB ----------
def apply_to_job(job_url):
    try:
        driver.get(job_url)
        time.sleep(4)

        apply_button = driver.find_element(By.XPATH, '//button[contains(text(), "Apply")]')
        apply_button.click()
        time.sleep(2)

        message_box = driver.find_element(By.NAME, "message")
        message_box.clear()
        message_box.send_keys("Hi! I'm really interested in this opportunity. Let's connect!")

        send_button = driver.find_element(By.XPATH, '//button[contains(text(), "Send Application")]')
        send_button.click()
        print(f"✅ Applied to {job_url}")
    except Exception as e:
        print(f"❌ Could not apply to {job_url}: {e}")

# ---------- MAIN FLOW ----------
try:
    login()
    job_links = scrape_job_links(limit=5)  # You can increase the limit
    for job_url in job_links:
        apply_to_job(job_url)
        time.sleep(2)
finally:
    driver.quit()
    print("🧹 Done.")
