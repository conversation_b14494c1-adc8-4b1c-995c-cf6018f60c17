#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time
import json

def setup_stealth_driver():
    """Set up Chrome driver with stealth options"""
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def extract_from_yuRUbf_containers():
    """
    Target the specific div.yuRUbf containers and extract job info
    """
    driver = None
    jobs = []
    
    try:
        print("🎯 TARGETING yuRUbf CONTAINERS - 5 RESULTS")
        print("=" * 50)
        
        driver = setup_stealth_driver()
        print("✅ Chrome driver created!")
        
        # Go to Google homepage first (helps avoid detection)
        print("🌐 Visiting Google homepage...")
        driver.get("https://www.google.com")
        time.sleep(2)
        
        # Navigate to search results
        search_url = "https://www.google.com/search?q=%22Full%20Stack%20Engineer%22+site:greenhouse.io+remote&tbs=qdr:d"
        print("🔍 Loading search results...")
        driver.get(search_url)
        time.sleep(4)
        
        # Check if blocked
        if "unusual traffic" in driver.page_source.lower():
            print("🚫 Blocked by Google")
            return []
        
        print("✅ Successfully loaded search results!")
        
        # Target the yuRUbf containers specifically
        yuRUbf_containers = driver.find_elements(By.CSS_SELECTOR, "div.yuRUbf")
        print(f"🎯 Found {len(yuRUbf_containers)} yuRUbf containers")
        
        if not yuRUbf_containers:
            print("❌ No yuRUbf containers found")
            return []
        
        # Extract info from each container
        job_links = []
        print(f"\n📋 Extracting from {len(yuRUbf_containers)} containers...")
        
        for i, container in enumerate(yuRUbf_containers, 1):
            try:
                print(f"\n🔍 Container {i}:")
                
                # Extract title (usually in h3 within the container)
                title = ""
                try:
                    title_element = container.find_element(By.CSS_SELECTOR, "h3")
                    title = title_element.text.strip()
                    print(f"   📝 Title: {title}")
                except Exception as e:
                    print(f"   ⚠️ No title found: {e}")
                
                # Extract link (usually the main link in the container)
                link = ""
                try:
                    link_element = container.find_element(By.CSS_SELECTOR, "a")
                    link = link_element.get_attribute("href")
                    print(f"   🔗 Link: {link}")
                except Exception as e:
                    print(f"   ⚠️ No link found: {e}")
                
                # Extract snippet/description if available
                snippet = ""
                try:
                    # Look for snippet in the parent container or nearby elements
                    parent = container.find_element(By.XPATH, "..")
                    snippet_element = parent.find_element(By.CSS_SELECTOR, ".VwiC3b, .s3v9rd, .IsZvec")
                    snippet = snippet_element.text.strip()
                    print(f"   📄 Snippet: {snippet[:100]}...")
                except Exception as e:
                    print(f"   ⚠️ No snippet found: {e}")
                
                if title and link:
                    job_links.append({
                        'title': title,
                        'link': link,
                        'snippet': snippet
                    })
                    print(f"   ✅ Successfully extracted job info")
                else:
                    print(f"   ❌ Missing title or link")
                
            except Exception as e:
                print(f"   ❌ Error processing container {i}: {e}")
                continue
        
        print(f"\n🎯 Successfully extracted {len(job_links)} job links!")
        
        if not job_links:
            print("❌ No job links extracted")
            return []
        
        # Now visit each job link for detailed information
        print(f"\n🚀 Visiting each of the {len(job_links)} job pages...")
        print("=" * 60)
        
        for i, job_info in enumerate(job_links, 1):
            print(f"\n📌 JOB {i}/{len(job_links)}: {job_info['title']}")
            print(f"🔗 URL: {job_info['link']}")
            
            try:
                # Open job page in new tab
                driver.execute_script("window.open('');")
                driver.switch_to.window(driver.window_handles[-1])
                driver.get(job_info['link'])
                time.sleep(3)
                
                # Extract detailed job information
                job_details = {
                    'title': job_info['title'],
                    'url': job_info['link'],
                    'google_snippet': job_info['snippet'],
                    'company': '',
                    'location': '',
                    'job_type': '',
                    'salary': '',
                    'description': '',
                    'requirements': '',
                    'benefits': '',
                    'application_url': job_info['link'],
                    'posted_date': '',
                    'source': 'Google Search -> Job Page'
                }
                
                # Extract company name
                company_selectors = [
                    ".company-name",
                    ".header-company-name",
                    "[data-qa='company-name']",
                    ".company",
                    "h1"
                ]
                for selector in company_selectors:
                    try:
                        company_element = driver.find_element(By.CSS_SELECTOR, selector)
                        company_text = company_element.text.strip()
                        if company_text and len(company_text) < 100:
                            job_details['company'] = company_text
                            break
                    except:
                        continue
                
                # Extract location
                location_selectors = [
                    ".location",
                    ".job-location",
                    "[data-qa='job-location']",
                    ".office-location",
                    "[class*='location']"
                ]
                for selector in location_selectors:
                    try:
                        location_element = driver.find_element(By.CSS_SELECTOR, selector)
                        location_text = location_element.text.strip()
                        if location_text:
                            job_details['location'] = location_text
                            break
                    except:
                        continue
                
                # Extract job description
                desc_selectors = [
                    ".job-post-content",
                    ".content",
                    ".description",
                    ".job-description",
                    "[data-qa='job-description']",
                    "main",
                    ".main-content"
                ]
                for selector in desc_selectors:
                    try:
                        desc_element = driver.find_element(By.CSS_SELECTOR, selector)
                        desc_text = desc_element.text.strip()
                        if len(desc_text) > 200:  # Only use substantial content
                            job_details['description'] = desc_text
                            break
                    except:
                        continue
                
                # Extract salary if available
                salary_selectors = [
                    ".salary",
                    "[data-qa='salary']",
                    "[class*='salary']",
                    "[class*='compensation']"
                ]
                for selector in salary_selectors:
                    try:
                        salary_element = driver.find_element(By.CSS_SELECTOR, selector)
                        salary_text = salary_element.text.strip()
                        if salary_text:
                            job_details['salary'] = salary_text
                            break
                    except:
                        continue
                
                # Look for application button/link
                try:
                    apply_selectors = [
                        ".btn-apply",
                        ".application-button",
                        "[data-qa='apply-button']",
                        "a[href*='apply']"
                    ]
                    for selector in apply_selectors:
                        try:
                            apply_element = driver.find_element(By.CSS_SELECTOR, selector)
                            apply_url = apply_element.get_attribute("href")
                            if apply_url:
                                job_details['application_url'] = apply_url
                                break
                        except:
                            continue
                except:
                    pass
                
                jobs.append(job_details)
                
                # Show what we extracted
                print(f"   ✅ Company: {job_details['company'] or 'Not found'}")
                print(f"   📍 Location: {job_details['location'] or 'Not found'}")
                print(f"   💰 Salary: {job_details['salary'] or 'Not specified'}")
                print(f"   📝 Description: {len(job_details['description'])} characters")
                print(f"   🎯 Apply URL: {job_details['application_url']}")
                
                # Close tab and return to main window
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
                
                # Small delay between requests
                time.sleep(2)
                
            except Exception as e:
                print(f"   ❌ Error visiting job page: {e}")
                # Make sure we close any extra tabs
                if len(driver.window_handles) > 1:
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])
                continue
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        return []
        
    finally:
        if driver:
            try:
                print("\n🕐 Keeping browser open for 20 seconds for review...")
                time.sleep(20)
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def save_job_results(jobs):
    """Save the extracted job results"""
    if not jobs:
        return
    
    try:
        # Save as JSON
        with open("yuRUbf_jobs.json", 'w', encoding='utf-8') as f:
            json.dump(jobs, f, indent=2, ensure_ascii=False)
        
        # Save as detailed text report
        with open("yuRUbf_jobs_report.txt", 'w', encoding='utf-8') as f:
            f.write("FULL STACK ENGINEER JOBS - DETAILED EXTRACTION\n")
            f.write("=" * 60 + "\n\n")
            
            for i, job in enumerate(jobs, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"Title: {job['title']}\n")
                f.write(f"Company: {job['company'] or 'Not specified'}\n")
                f.write(f"Location: {job['location'] or 'Not specified'}\n")
                f.write(f"Salary: {job['salary'] or 'Not specified'}\n")
                f.write(f"Job URL: {job['url']}\n")
                f.write(f"Application URL: {job['application_url']}\n")
                f.write(f"Source: {job['source']}\n")
                f.write(f"\nGoogle Snippet:\n{job['google_snippet']}\n")
                f.write(f"\nFull Description:\n{job['description'][:2000]}{'...' if len(job['description']) > 2000 else ''}\n")
                f.write("\n" + "=" * 60 + "\n\n")
        
        print(f"💾 Results saved to yuRUbf_jobs.json and yuRUbf_jobs_report.txt")
        
    except Exception as e:
        print(f"❌ Error saving results: {e}")

def main():
    """Main function"""
    print("=" * 70)
    print("🎯 yuRUbf CONTAINER JOB SCRAPER")
    print("🔍 Targeting the 5 specific Google search result containers")
    print("=" * 70)
    
    jobs = extract_from_yuRUbf_containers()
    
    if jobs:
        print(f"\n🎉 SUCCESS! Extracted detailed info from {len(jobs)} jobs!")
        
        print("\n📋 FINAL SUMMARY:")
        print("-" * 50)
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job['title']}")
            print(f"   🏢 Company: {job['company'] or 'Unknown'}")
            print(f"   📍 Location: {job['location'] or 'Not specified'}")
            print(f"   💰 Salary: {job['salary'] or 'Not specified'}")
            print(f"   🔗 Job URL: {job['url']}")
            print(f"   🎯 Apply URL: {job['application_url']}")
            print()
        
        save_job_results(jobs)
        
        print("🎉 Job extraction completed successfully!")
        
    else:
        print("\n❌ No jobs extracted")
        print("💡 This could mean:")
        print("   • The yuRUbf containers don't contain job links")
        print("   • The links are not accessible")
        print("   • Need to adjust extraction logic")

if __name__ == "__main__":
    main()
