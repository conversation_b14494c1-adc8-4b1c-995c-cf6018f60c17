#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys
import time
import urllib.parse

def setup_driver():
    """Set up Chrome driver"""
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    # Keep browser visible
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    return driver

def google_search_jobs(search_query):
    """
    Search Google for jobs and scrape the results
    """
    driver = None
    jobs = []
    
    try:
        print(f"🚀 Starting Google job search for: {search_query}")
        
        driver = setup_driver()
        print("✅ Chrome driver created successfully!")
        
        # Go to Google
        print("🌐 Opening Google...")
        driver.get("https://www.google.com")
        time.sleep(2)
        
        # Find search box and enter query
        search_box = driver.find_element(By.NAME, "q")
        search_box.clear()
        search_box.send_keys(search_query)
        search_box.send_keys(Keys.RETURN)
        
        print(f"🔍 Searching for: {search_query}")
        time.sleep(3)
        
        # Get search results
        print("📋 Extracting search results...")
        
        # Try multiple selectors for search results
        search_results = []

        # Try different CSS selectors that Google uses
        selectors_to_try = [
            "div.g",           # Traditional search results
            "div[data-ved]",   # Alternative selector
            ".g",              # Simple class selector
            "[data-ved] h3",   # Results with h3 headers
        ]

        for selector in selectors_to_try:
            try:
                results = driver.find_elements(By.CSS_SELECTOR, selector)
                if results:
                    search_results = results
                    print(f"🔎 Found {len(search_results)} search results using selector: {selector}")
                    break
            except:
                continue

        if not search_results:
            print("⚠️ No search results found with any selector. Let's check the page content...")
            # Print page title and some content for debugging
            print(f"Page title: {driver.title}")
            page_text = driver.find_element(By.TAG_NAME, "body").text
            print(f"Page content preview: {page_text[:500]}...")
            return []
        
        for i, result in enumerate(search_results[:15]):  # Check more results
            try:
                # Try multiple ways to extract title and link
                title = ""
                link = ""
                description = ""

                # Try to find title
                title_selectors = ["h3", ".LC20lb", ".DKV0Md"]
                for selector in title_selectors:
                    try:
                        title_element = result.find_element(By.CSS_SELECTOR, selector)
                        title = title_element.text.strip()
                        if title:
                            break
                    except:
                        continue

                # Try to find link
                link_selectors = ["a", "h3 a", ".yuRUbf a"]
                for selector in link_selectors:
                    try:
                        link_element = result.find_element(By.CSS_SELECTOR, selector)
                        link = link_element.get_attribute("href")
                        if link and link.startswith("http"):
                            break
                    except:
                        continue

                # Try to find description
                desc_selectors = [".VwiC3b", ".s3v9rd", ".st", "span[data-st]", ".IsZvec"]
                for selector in desc_selectors:
                    try:
                        desc_element = result.find_element(By.CSS_SELECTOR, selector)
                        description = desc_element.text.strip()
                        if description:
                            break
                    except:
                        continue

                if not description:
                    description = "No description available"

                # Debug: print what we found
                if title or link:
                    print(f"🔍 Result {i+1}: Title='{title}', Link='{link[:50]}...'")

                # Only include greenhouse.io results
                if link and "greenhouse.io" in link.lower():
                    job = {
                        'title': title,
                        'link': link,
                        'description': description,
                        'source': 'Google Search'
                    }
                    jobs.append(job)

                    print(f"\n📌 Job {len(jobs)}:")
                    print(f"   Title: {title}")
                    print(f"   Link: {link}")
                    print(f"   Description: {description[:100]}...")

            except Exception as e:
                print(f"⚠️ Error extracting result {i+1}: {e}")
                continue
        
        # Keep browser open so you can see the results
        print(f"\n🕐 Keeping browser open for 15 seconds so you can see the search results...")
        print("💡 You can manually browse the results or wait for auto-close")
        time.sleep(15)
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return []
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def save_jobs_to_file(jobs, filename="greenhouse_jobs.txt"):
    """Save jobs to a text file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("FULL STACK ENGINEER JOBS FROM GREENHOUSE.IO\n")
            f.write("=" * 50 + "\n\n")
            
            for i, job in enumerate(jobs, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"Title: {job['title']}\n")
                f.write(f"Link: {job['link']}\n")
                f.write(f"Description: {job['description']}\n")
                f.write(f"Source: {job['source']}\n")
                f.write("-" * 40 + "\n\n")
        
        print(f"💾 Jobs saved to: {filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving jobs: {e}")
        return False

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 GOOGLE JOB SCRAPER FOR GREENHOUSE.IO")
    print("=" * 60)
    
    # The search query you requested
    search_query = '"Full Stack Engineer" site:greenhouse.io remote'
    
    # Search for jobs
    jobs = google_search_jobs(search_query)
    
    if jobs:
        print(f"\n✅ Found {len(jobs)} Full Stack Engineer jobs on Greenhouse.io!")
        print("\n📋 SUMMARY OF JOBS FOUND:")
        print("-" * 40)
        
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job['title']}")
            print(f"   🔗 {job['link']}")
            print()
        
        # Ask if user wants to save results
        save_choice = input("💾 Save results to file? (y/n): ").lower().strip()
        if save_choice == 'y':
            save_jobs_to_file(jobs)
        
        # Ask if user wants to see full details
        details_choice = input("📖 Show full job details? (y/n): ").lower().strip()
        if details_choice == 'y':
            print("\n" + "=" * 60)
            print("📖 FULL JOB DETAILS:")
            print("=" * 60)
            
            for i, job in enumerate(jobs, 1):
                print(f"\nJOB {i}:")
                print(f"Title: {job['title']}")
                print(f"Link: {job['link']}")
                print(f"Description: {job['description']}")
                print("-" * 40)
    else:
        print("❌ No jobs found. Try adjusting the search query.")

if __name__ == "__main__":
    main()
