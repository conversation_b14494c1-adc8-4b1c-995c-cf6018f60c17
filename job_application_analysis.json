[{"url": "https://boards.greenhouse.io/waymo/jobs/6508003", "page_info": {"title": "Fullstack Engineer - Waymo Applications and Tools - Warsaw, Masovian Voivodeship, Poland", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://careers.withwaymo.com/call_to_actions/36ee3fafcbec8a9d447913670f178bc5/form_submissions?job_id=080a688543c7e393d34ad27c0e156169&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_job_alert_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_departments_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_locations_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_1_3", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 1, "action": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}, {"index": 2, "action": "https://careers.withwaymo.com/call_to_actions/4e392923843c0621f469608bfdf96a90/form_submissions?job_id=080a688543c7e393d34ad27c0e156169&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "id": "question_7_0_4_0_0", "placeholder": "", "required": true, "class": "form-control"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][1c2f1d08be9b296bd7dd6b05ed4d6fc5]", "id": "question_7_0_4_0_1", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][cb17eaabcb24317cee5894a18a7ae510]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][557d36d5c53d3670b3d66be2dec97909]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][4][kind]", "id": "form_field_kind_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][4][form_template_field_uid]", "id": "form_form_template_field_id_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_job_alert_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_field_kind_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][form_template_field_uid]", "id": "form_form_template_field_id_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][agreement]", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][5][agreement]", "id": "form_agreement_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][department_uids][]", "id": "form_departments_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][locations_json][]", "id": "form_locations_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "candidate_referral_uid", "id": "candidate_referral_uid_7_0", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 3, "action": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}], "buttons": [{"text": "Toggle navigation", "type": "button", "href": "", "class": "navbar-toggler collapsed", "id": "", "data_qa": ""}, {"text": "Share", "type": "submit", "href": "", "class": "IN-e146f606-483d-4824-a598-79a778fddfb4-1G9ISYhSF8XoOmdcl0yKDu", "id": "", "data_qa": ""}, {"text": "Share", "type": "submit", "href": "", "class": "facebook-share-button", "id": "", "data_qa": ""}, {"text": "Selected country", "type": "button", "href": "", "class": "iti__selected-country", "id": "", "data_qa": ""}, {"text": "Close", "type": "button", "href": "", "class": "btn-close", "id": "", "data_qa": ""}, {"text": "Manage Cookies", "type": "submit", "href": "", "class": "btn btn-light", "id": "manage_cookie_preferences", "data_qa": ""}, {"text": "I do not accept", "type": "button", "href": "", "class": "btn btn-info consent-reject", "id": "consent_reject", "data_qa": ""}, {"text": "I accept", "type": "button", "href": "", "class": "btn btn-success consent-agree", "id": "consent_agree", "data_qa": ""}, {"text": "Search jobs", "type": "", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn", "id": "", "data_qa": ""}, {"text": "Apply now", "type": "", "href": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003#apply", "class": "button button4", "id": "", "data_qa": ""}], "file_inputs": [{"name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}, {"name": "form_submission[fields_attributes][4][job_questions][1c2f1d08be9b296bd7dd6b05ed4d6fc5]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}], "text_inputs": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][cb17eaabcb24317cee5894a18a7ae510]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][557d36d5c53d3670b3d66be2dec97909]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}], "links": [{"text": "Early careers", "href": "https://careers.withwaymo.com/early-careers", "class": "nav-link"}, {"text": "Apply now", "href": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003#apply", "class": "button button4"}, {"text": "Search jobs", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn"}], "page_text": "Why Waymo\nWorking at Waymo\nTeams\nEarly careers\nHow we hire\nOpen roles\nVisit Waymo.com\nFullstack Engineer - Waymo Applications and Tools\nWARSAW, MASOVIAN VOIVODESHIP, POLAND FULL-TIME SOFTWARE ENGINEERING 3280\nApply now\nWaymo is an autonomous driving technology company with the mission to be the most trusted driver. Since its start as the Google Self-Driving Car Project in 2009, Waymo has focused on building the Waymo Driver—The World's Most Experienced Driver™—to improve access to mobility while saving thousands of lives now lost to traffic crashes. The Waymo Driver powers Waymo One, a fully autonomous ride-hailing service, and can also be applied to a range of vehicle platforms and product use cases. The Waymo Driver has provided over one million rider-only trips, enabled by its experience autonomously driving tens of millions of miles on public roads and tens of billions in simulation across 13+ U.S. states.\nThe Waymo Commercialization team works on Waymo One, our public, fully autonomous ride-hailing service. Riders can use the Waymo One app to hail one of our vehicles (with no human driver in the front seat!) 24 hours a day, 7 days a week. Our riders get a clean vehicle every time and a Driver with more than 20 million miles of experience on public roads.\nTweet\nShare\nJob Alerts\nSign up to receive tailored job alerts delivered straight to your inbox!\nA personal contact at Google/Alphabet told me about the opportunity\nA personal contact at Waymo told me about the opportunity\nAcademic Contact (professor recommendation, classmate, etc.)\nAgency Recruiter Outreach\nAlphabet's Internal Job Board (Grow)\nGlassdoor\nHandshake\nIndeed\nLinkedIn\nMedia (blog post, news article, etc.)\nSocial Media (Facebook, Twitter, etc.)\nUniversity Event (info session, tech talk, luncheon, etc.)\nUniversity Job Board\nWaymo Careers Page\nWaymo Industry Event (conference, career fair, etc.)\nWaymo Recruiter Outreach\nWord of Mouth\nOther\nI am authorized to work for any employer in the country in which", "current_url": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003"}, "application_analysis": {"application_type": "external_redirect", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": true, "apply_button_found": true, "required_fields": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "placeholder": "Phone Number"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "placeholder": ""}], "file_upload_fields": [{"name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}, {"name": "form_submission[fields_attributes][4][job_questions][1c2f1d08be9b296bd7dd6b05ed4d6fc5]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly", "Application requires redirect to: https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003#apply"]}, "timestamp": 1751870594.80573}, {"url": "https://boards.greenhouse.io/coveoen/jobs/7944459002", "page_info": {"title": "Software Developer - Machine Learning | Montreal (Province of Quebec, Canada) | Coveo", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002", "method": "get", "class": "", "inputs": [{"type": "hidden", "name": "MarketoDetailIDUrl", "id": "MarketoDetailIDUrl", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "application_number", "id": "application_number", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmadgroup", "id": "utmadgroup", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utm_campaign", "id": "utm_campaign", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utm_channel", "id": "utm_channel", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmcontent", "id": "utmcontent", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utm_medium", "id": "utm_medium", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmsource", "id": "utmsource", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmterm", "id": "utmterm", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "mapped_url_token", "id": "mapped_url_token", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "first_name", "id": "first_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "last_name", "id": "last_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "email", "id": "email", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "phone", "id": "phone", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "location", "id": "location", "placeholder": "", "required": true, "class": " "}, {"type": "file", "name": "resume_content", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "resume_content_validator", "id": "", "placeholder": "", "required": false, "class": "validateFile"}], "textareas": [{"name": "resume_text", "id": "resume", "placeholder": "", "required": true, "class": "materialize-textarea  "}, {"name": "question_32237734002", "id": "question_32237734002", "placeholder": "", "required": true, "class": "materialize-textarea  "}]}, {"index": 1, "action": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002", "method": "get", "class": "", "inputs": [{"type": "email", "name": "Email", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "FirstName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "LastName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Country", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "State", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Phone", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Title", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Company", "id": "", "placeholder": "", "required": false, "class": ""}], "textareas": []}], "buttons": [{"text": "I don't check all the boxes. Should I still apply?", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-0", "data_qa": ""}, {"text": "What is Cove<PERSON>?", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-1", "data_qa": ""}, {"text": "What is it like to work at Coveo?", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-2", "data_qa": ""}, {"text": "Work Modes: In-Person, Hybrid and Remote", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-3", "data_qa": ""}, {"text": "Submit Application", "type": "submit", "href": "", "class": " button primary ", "id": "submit_application", "data_qa": ""}, {"text": "OK", "type": "submit", "href": "", "class": "", "id": "onetrust-accept-btn-handler", "data_qa": ""}, {"text": "<PERSON><PERSON> Settings", "type": "submit", "href": "", "class": "", "id": "onetrust-pc-btn-handler", "data_qa": ""}, {"text": "Close", "type": "submit", "href": "", "class": "onetrust-close-btn-handler onetrust-close-btn-ui banner-close-button ot-close-icon", "id": "", "data_qa": ""}, {"text": "Close", "type": "submit", "href": "", "class": "ot-close-icon", "id": "close-pc-btn-handler", "data_qa": ""}, {"text": "Vendor Details button opens Vendor List menu", "type": "submit", "href": "", "class": "ot-link-btn category-host-list-handler", "id": "", "data_qa": ""}, {"text": "Vendor Details button opens Vendor List menu", "type": "submit", "href": "", "class": "ot-link-btn category-host-list-handler", "id": "", "data_qa": ""}, {"text": "Vendor Details button opens Vendor List menu", "type": "submit", "href": "", "class": "ot-link-btn category-host-list-handler", "id": "", "data_qa": ""}, {"text": "Vendor Details button opens Vendor List menu", "type": "submit", "href": "", "class": "ot-link-btn category-host-list-handler", "id": "", "data_qa": ""}, {"text": "Back", "type": "submit", "href": "", "class": "ot-link-btn back-btn-handler", "id": "", "data_qa": ""}, {"text": "Filter", "type": "submit", "href": "", "class": "", "id": "filter-btn-handler", "data_qa": ""}, {"text": "Search", "type": "", "href": "", "class": "CoveoSearchButton coveo-accessible-button", "id": "", "data_qa": ""}, {"text": "Contact Us", "type": "", "href": "https://www.coveo.com/en/contact", "class": "button simple ", "id": "", "data_qa": ""}, {"text": "Pricing", "type": "", "href": "https://www.coveo.com/en/pricing", "class": "button simple ", "id": "", "data_qa": ""}], "file_inputs": [{"name": "resume_content", "id": "", "accept": "", "multiple": false, "class": ""}], "text_inputs": [{"type": "text", "name": "", "id": "", "placeholder": "What are you looking for?", "required": false, "class": ""}, {"type": "text", "name": "first_name", "id": "first_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "last_name", "id": "last_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "email", "id": "email", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "phone", "id": "phone", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "location", "id": "location", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "resume_content_validator", "id": "", "placeholder": "", "required": false, "class": "validateFile"}, {"type": "text", "name": "FirstName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "LastName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Country", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "State", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Phone", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Title", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Company", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "vendor-search-handler", "id": "vendor-search-handler", "placeholder": "Search…", "required": false, "class": ""}, {"type": "email", "name": "Email", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "textarea", "name": "resume_text", "id": "resume", "placeholder": "", "required": true, "class": "materialize-textarea  "}, {"type": "textarea", "name": "question_32237734002", "id": "question_32237734002", "placeholder": "", "required": true, "class": "materialize-textarea  "}], "links": [{"text": "careers", "href": "https://www.coveo.com/en/company/careers", "class": "href-container"}, {"text": "Our job openings", "href": "https://www.coveo.com/en/company/careers/open-positions", "class": "trigger-submenu"}, {"text": "Apply", "href": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002#job-form", "class": "primary-white-large"}, {"text": "Careers", "href": "https://www.coveo.com/en/company/careers", "class": "menu-link "}], "page_text": "careers\nCoveo.com\nHome\nOur job openings\nOur culture\nOur Team\nInternships\nSoftware Developer - Machine Learning\nMontreal (Province of Quebec, Canada)\nMachine Learning\nFull-time\nRemote or Hybrid\nApply\n< See All Positions\n  Enable NLP technology reuse across the company\nAs a Software Developer in Machine Learning, you will play a key role in supporting teams of applied scientists and ML developers who train, evaluate, and use a variety of NLP models, including LLMs.\nYour team is the CoreNLP team, a mix of scientists and developers who provide reusable NLP technologies that accelerate delivery for the other teams in your unit. Your mission is to contribute to prototyping, productionizing, and maintaining the NLP technologies that power some of Coveo's most visible AI capabilities.\nHere is what makes this opportunity exciting:\nYour team is uniquely positioned to impact Coveo’s research and development efforts and offers one of the best environments to quickly get up to speed with state-of-the-art NLP technology.\nThe ML unit at Coveo focuses on finding ways to apply the latest advances in Recommender Systems, Ranking Optimization, LLMs and NLP to build innovative solutions in commerce, self-service and other business verticals. We solve real problems with real data, for hundreds of large enterprise clients all around the world, on a modern platform that serves over 100M requests and automatically trains thousands of ML models on a daily basis. \nHere is a glimpse at your responsibilities:\nParticipate directly in every aspect of NLP technology delivery: requirements gathering, conception, implementation, automated testing, release, monitoring, maintenance, etc.\nAlong with the rest of your team, make continuous learning a weekly practice that ensures awareness of emerging opportunities in the field.\nIdentify current pain points in NLP research and development and deliver software that addresses them.\nEngage with your community of peers to challenge the status quo, improve ou", "current_url": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002"}, "application_analysis": {"application_type": "external_redirect", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": true, "apply_button_found": true, "required_fields": [{"type": "text", "name": "first_name", "placeholder": ""}, {"type": "text", "name": "last_name", "placeholder": ""}, {"type": "text", "name": "email", "placeholder": ""}, {"type": "text", "name": "phone", "placeholder": ""}, {"type": "text", "name": "location", "placeholder": ""}], "file_upload_fields": [{"name": "resume_content", "id": "", "accept": "", "multiple": false, "class": ""}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly", "Application requires redirect to: https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002#job-form"]}, "timestamp": 1751870632.023498}, {"url": "https://boards.greenhouse.io/waymo/jobs/6885682", "page_info": {"title": "Design Engineer, ASIC - Mountain View, California, United States", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://careers.withwaymo.com/call_to_actions/36ee3fafcbec8a9d447913670f178bc5/form_submissions?job_id=25855ed77e829b9ed610f2db8988a8f0&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_job_alert_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_departments_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_locations_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_1_3", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 1, "action": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}, {"index": 2, "action": "https://careers.withwaymo.com/call_to_actions/4e392923843c0621f469608bfdf96a90/form_submissions?job_id=25855ed77e829b9ed610f2db8988a8f0&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "id": "question_7_0_4_0_0", "placeholder": "", "required": true, "class": "form-control"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][22037104a68ba0da1dcc90fd9f0456e1]", "id": "question_7_0_4_0_1", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][a406c0b96a2da3d8fb0842bdca990c39]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][65d22f5f7e35bafcd6ce2209cabf1456]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "form_submission_fields_attributes_4_job_questions_bbd797064c388ae2fbd3e97fbccc267c_job_question_option_uid_", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_5e8501b92b8024b3bb3b8a07d11520cb", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_39be16ebb368cef9f62a585ecb7fa9d8", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_6d0c84c3ab5a293d35c712530c78785b", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_b23402f513f3384c2311a00b61563cda", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_9efd329f738608c27a829bd6ce2cddf9", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_7e588b223f7770c7bd4e896904760473", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_e7ad7d0352e4dcedd00960b3da9ab52b", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][4][kind]", "id": "form_field_kind_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][4][form_template_field_uid]", "id": "form_form_template_field_id_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_job_alert_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_field_kind_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][form_template_field_uid]", "id": "form_form_template_field_id_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][agreement]", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][5][agreement]", "id": "form_agreement_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][department_uids][]", "id": "form_departments_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][locations_json][]", "id": "form_locations_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "candidate_referral_uid", "id": "candidate_referral_uid_7_0", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 3, "action": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}], "buttons": [{"text": "Toggle navigation", "type": "button", "href": "", "class": "navbar-toggler collapsed", "id": "", "data_qa": ""}, {"text": "Share", "type": "submit", "href": "", "class": "IN-e146f606-483d-4824-a598-79a778fddfb4-1G9ISYhSF8XoOmdcl0yKDu", "id": "", "data_qa": ""}, {"text": "Share", "type": "submit", "href": "", "class": "facebook-share-button", "id": "", "data_qa": ""}, {"text": "Notify me", "type": "submit", "href": "", "class": "btn btn-primary", "id": "form_submit_1_3", "data_qa": ""}, {"text": "Previous", "type": "button", "href": "", "class": "carousel-control-prev", "id": "", "data_qa": ""}, {"text": "Next", "type": "button", "href": "", "class": "carousel-control-next", "id": "", "data_qa": ""}, {"text": "Ethiopia +251", "type": "button", "href": "", "class": "iti__selected-country", "id": "", "data_qa": ""}, {"text": "Submit your application", "type": "submit", "href": "", "class": "btn btn-primary", "id": "form_submit_7_0", "data_qa": ""}, {"text": "Close", "type": "button", "href": "", "class": "btn-close", "id": "", "data_qa": ""}, {"text": "Add to favorites", "type": "", "href": "", "class": "btn btn-primary candidate-favourite", "id": "link_candidate_favourite_1_2", "data_qa": ""}, {"text": "View favorites", "type": "", "href": "https://careers.withwaymo.com/me/settings", "class": "btn btn-primary view-favourites", "id": "link_view_favourites_1_2", "data_qa": ""}, {"text": "Search jobs", "type": "", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn", "id": "", "data_qa": ""}, {"text": "Apply now", "type": "", "href": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#apply", "class": "button button4", "id": "", "data_qa": ""}, {"text": "Learn more", "type": "", "href": "https://careers.withwaymo.com/benefits", "class": "button button2", "id": "", "data_qa": ""}], "file_inputs": [{"name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}, {"name": "form_submission[fields_attributes][4][job_questions][22037104a68ba0da1dcc90fd9f0456e1]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}], "text_inputs": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][a406c0b96a2da3d8fb0842bdca990c39]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][65d22f5f7e35bafcd6ce2209cabf1456]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}], "links": [{"text": "Early careers", "href": "https://careers.withwaymo.com/early-careers", "class": "nav-link"}, {"text": "Apply now", "href": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#apply", "class": "button button4"}, {"text": "Search jobs", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn"}], "page_text": "Why <PERSON><PERSON>\nWorking at Waymo\nTeams\nEarly careers\nHow we hire\nOpen roles\nVisit Waymo.com\nDesign Engineer, ASIC\nMOUNTAIN VIEW, CALIFORNIA, UNITED STATES FULL-TIME HARDWARE ENGINEERING 3496\nApply now\nAdd to favorites View favorites\nWaymo is an autonomous driving technology company with the mission to be the most trusted driver. Since its start as the Google Self-Driving Car Project in 2009, Waymo has focused on building the Waymo Driver—The World's Most Experienced Driver™—to improve access to mobility while saving thousands of lives now lost to traffic crashes. The Waymo Driver powers Waymo One, a fully autonomous ride-hailing service, and can also be applied to a range of vehicle platforms and product use cases. The Waymo Driver has provided over one million rider-only trips, enabled by its experience autonomously driving tens of millions of miles on public roads and tens of billions in simulation across 13+ U.S. states.\nWaymo's Compute Team is tasked with a critical and exciting mission: We deliver the compute platform responsible for running the fully autonomous vehicle's software stack. To achieve our mission, we architect and create high-performance custom silicon; we develop system-level compute architectures that push the boundaries of performance, power, and latency; and we collaborate closely with many other teammates to ensure we design and optimize hardware and software for maximum performance. We are a multidisciplinary team seeking curious and talented teammates to work on one of the world's highest performance automotive compute platforms.\nIn this hybrid role, you'll report to an ASIC Design Manager\nYou will:\nWork with researchers and architects to translate high level requirements into hardware features\nSpecify, architect, and design hardware accelerators that process sensor data and other system compute workloads\nPerform power, area and performance trade-off analyses of digital designs\nWork with verification teams to guarantee functional correctness and ", "current_url": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682"}, "application_analysis": {"application_type": "external_redirect", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": true, "apply_button_found": true, "required_fields": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "placeholder": "Phone Number"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "placeholder": ""}], "file_upload_fields": [{"name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}, {"name": "form_submission[fields_attributes][4][job_questions][22037104a68ba0da1dcc90fd9f0456e1]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control"}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly", "Application requires redirect to: https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#apply"]}, "timestamp": 1751870645.972092}, {"url": "http://job-boards.greenhouse.io/nerdy/jobs/7045082", "page_info": {"title": "Job Application for Director of Supply Operations at Nerdy", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://job-boards.greenhouse.io/nerdy/jobs/7045082", "method": "get", "class": "application--form", "inputs": [{"type": "text", "name": "", "id": "first_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "last_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "preferred_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "email", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "phone", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "file", "name": "", "id": "resume", "placeholder": "", "required": false, "class": "visually-hidden"}, {"type": "file", "name": "", "id": "cover_letter", "placeholder": "", "required": false, "class": "visually-hidden"}, {"type": "text", "name": "", "id": "question_58292628", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292629", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292630", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292631", "placeholder": "", "required": false, "class": "select__input"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": true, "class": "remix-css-1a0ro4n-requiredInput"}, {"type": "text", "name": "", "id": "question_58292632", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292633", "placeholder": "", "required": false, "class": "input input__single-line"}], "textareas": []}], "buttons": [{"text": "Apply", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Autofill with Greenhouse", "type": "button", "href": "", "class": "btn btn--pill btn--secondary", "id": "", "data_qa": ""}, {"text": "Attach", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Dropbox", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Google Drive", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Enter manually", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Attach", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Dropbox", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Google Drive", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Enter manually", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}, {"text": "Toggle flyout", "type": "button", "href": "", "class": "icon-button icon-button--sm", "id": "", "data_qa": ""}, {"text": "Submit application", "type": "submit", "href": "", "class": "btn btn--pill", "id": "", "data_qa": ""}], "file_inputs": [{"name": "", "id": "resume", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden"}, {"name": "", "id": "cover_letter", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden"}], "text_inputs": [{"type": "text", "name": "", "id": "first_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "last_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "preferred_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "email", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "phone", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292628", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292629", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292630", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292631", "placeholder": "", "required": false, "class": "select__input"}, {"type": "text", "name": "", "id": "question_58292632", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292633", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "textarea", "name": "g-recaptcha-response", "id": "g-recaptcha-response-100000", "placeholder": "", "required": false, "class": "g-recaptcha-response"}], "links": [], "page_text": "New\nDirector of Supply Operations\nRemote - Dallas\nApply\nOverview:\n<PERSON><PERSON><PERSON> is seeking a Director of Supply Operations to lead and optimize the supply-side marketplace operations for our consumer business. The Director will own operational processes around tutor forecasting, sourcing, subject-matter vetting, and marketplace excellence, leveraging automation and AI to ensure our tutor supply aligns seamlessly with client demand and to drive continuous improvement as we scale.\nThis role is strategically vital for scaling operations, maintaining marketplace health, and driving rapid growth across our learning platform. We are seeking a strategically-minded, analytically rigorous leader—someone who thrives in marketplace operations, excels at forecasting and supply optimization, and can independently dive into data, develop operational models, and collaborate with Product and Engineering to prototype AI-based solutions. The ideal candidate has experience navigating data-rich environments and seamlessly translating strategy into hands-on execution.\nAbout Nerdy:\nAt Nerdy (NYSE: NRDY) -  the company behind Varsity Tutors - we’re redrawing the blueprint of learning. Our Live + AI™ platform fuses real-time human expertise with proprietary generative-AI systems, setting a new bar for measurable academic impact at global scale. \nWe recruit the kind of technologists and operators you’d bet on as solo founders - people who turn ambiguous problems into shipping code, iterate faster than markets move, and compound their advantage with every data point. In an era where great employees can deliver 10-times the leverage of the merely good, we back those who play to win.\nFortune favors the bold. Join us.\nHow we compete:\nAI-Native at every level\nFrom the CEO to day-one hires, everyone builds and ships with generative AI. If you’re not wielding AI, you’re not done.\nEntrepreneurial velocity\nMove at founder speed, prototype in hours, and measure in real user outcomes. Slow teams die.\nFree-mar", "current_url": "https://job-boards.greenhouse.io/nerdy/jobs/7045082"}, "application_analysis": {"application_type": "direct_form", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": false, "apply_button_found": true, "required_fields": [{"type": "text", "name": "", "placeholder": ""}], "file_upload_fields": [{"name": "", "id": "resume", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden"}, {"name": "", "id": "cover_letter", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden"}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly", "✅ Greenhouse platform detected - standard application flow expected"], "platform": "greenhouse"}, "timestamp": 1751870656.739586}, {"url": "https://boards.greenhouse.io/squarespace/jobs/7036031", "page_info": {"title": "Sales Manager, Cross-Sell & Expansion – Careers – Squarespace", "company": "", "location": "", "description": "", "forms": [], "buttons": [{"text": "PRODUCTS", "type": "submit", "href": "", "class": "cta cta--inline cta--light global-navigation__header-link-item", "id": "", "data_qa": ""}, {"text": "RESOURCES", "type": "submit", "href": "", "class": "cta cta--inline cta--light global-navigation__header-link-item", "id": "", "data_qa": ""}, {"text": "Account information dropdown menu", "type": "submit", "href": "", "class": "cta cta--inline cta--light global-navigation__account-avatar", "id": "", "data_qa": ""}, {"text": "Navigation menu", "type": "submit", "href": "", "class": "cta cta--inline cta--light global-navigation__hamburger", "id": "", "data_qa": ""}, {"text": "Select language:", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": ""}, {"text": "Select currency:", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": ""}, {"text": "USD", "type": "submit", "href": "", "class": "footer__locale-setting-option footer__locale-setting-option--is-active", "id": "", "data_qa": ""}, {"text": "EUR", "type": "submit", "href": "", "class": "footer__locale-setting-option", "id": "", "data_qa": ""}, {"text": "English", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": ""}, {"text": "$ USD", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": ""}, {"text": "USD", "type": "submit", "href": "", "class": "footer__locale-setting-option footer__locale-setting-option--is-active", "id": "", "data_qa": ""}, {"text": "EUR", "type": "submit", "href": "", "class": "footer__locale-setting-option", "id": "", "data_qa": ""}], "file_inputs": [], "text_inputs": [], "links": [{"text": "Careers", "href": "https://www.squarespace.com/about/careers", "class": "cta cta--inline cta--light footer__link-item-link"}], "page_text": "PRODUCTS\nRESOURCES\nLOG IN\nGET STARTED\nCHANNELS AND SERVICES\nSales Manager, Cross-Sell & Expansion\nA website makes it real\nWe are an ICANN accredited registrar.\nProducts\nWebsite Templates\nWebsites\nDomains\nAI Website Builder\nDesign Intelligence\nOnline Stores\nServices\nInvoicing\nScheduling\nContent & Memberships\nDonations\nPayments\nMarketing Tools\nEmail Campaigns\nProfessional Email\nFeature List\nPricing\nSolutions\nCustomer Examples\nSquarespace Collection\nFitness\nBeauty\nPhotography\nRestaurants\nArt & Design\nWedding\nCreators\nEnterprise\nPrograms\nFor Professionals\nAffiliates\nSupport\nHelp Center\nForum\nWebinars\nHire an Expert\nDeveloper Blog\nDeveloper Platform\nSystem Status\nResources\nExtensions\nSquarespace Blog\nFree Tools\nBusiness Name Generator\nLogo Maker\nCompany\nAbout\nCareers\nOur History\nOur Brand\nAccessibility\nNewsroom\nPress & Media\nContact Us\nFollow\nInstagram\nYoutube\nLinkedin\nFacebook\nX\nEnglish\n$ USD\nTerms\nPrivacy\nSecurity Measures\nSitemap\n© 2025 Squarespace, Inc.", "current_url": "https://www.squarespace.com/careers/jobs/7036031"}, "application_analysis": {"application_type": "unknown", "has_direct_form": false, "has_file_upload": false, "requires_external_redirect": false, "apply_button_found": false, "required_fields": [], "file_upload_fields": [], "application_steps": ["1. Manual review required - no clear application path found"], "recommendations": ["⚠️ No clear application process detected - manual review needed"]}, "timestamp": 1751870672.2290921}]