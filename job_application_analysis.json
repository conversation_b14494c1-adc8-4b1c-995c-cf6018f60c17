[{"url": "https://boards.greenhouse.io/waymo/jobs/6508003", "page_info": {"title": "Fullstack Engineer - Waymo Applications and Tools - Warsaw, Masovian Voivodeship, Poland", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://careers.withwaymo.com/call_to_actions/36ee3fafcbec8a9d447913670f178bc5/form_submissions?job_id=080a688543c7e393d34ad27c0e156169&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_job_alert_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_departments_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_locations_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_1_3", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 1, "action": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}, {"index": 2, "action": "https://careers.withwaymo.com/call_to_actions/4e392923843c0621f469608bfdf96a90/form_submissions?job_id=080a688543c7e393d34ad27c0e156169&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "id": "question_7_0_4_0_0", "placeholder": "", "required": true, "class": "form-control"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][1c2f1d08be9b296bd7dd6b05ed4d6fc5]", "id": "question_7_0_4_0_1", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][cb17eaabcb24317cee5894a18a7ae510]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][557d36d5c53d3670b3d66be2dec97909]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][4][kind]", "id": "form_field_kind_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][4][form_template_field_uid]", "id": "form_form_template_field_id_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_job_alert_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_field_kind_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][form_template_field_uid]", "id": "form_form_template_field_id_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][agreement]", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][5][agreement]", "id": "form_agreement_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][department_uids][]", "id": "form_departments_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][locations_json][]", "id": "form_locations_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "candidate_referral_uid", "id": "candidate_referral_uid_7_0", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 3, "action": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}], "buttons": [{"text": "Toggle navigation", "type": "button", "href": "", "class": "navbar-toggler collapsed", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Selected country", "type": "button", "href": "", "class": "iti__selected-country", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Close", "type": "button", "href": "", "class": "btn-close", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Save", "type": "button", "href": "", "class": "btn btn-success consent-agree", "id": "consent_save", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Search jobs", "type": "", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "A personal contact at Google/Alphabet told me about the opportunity\nA personal contact at Waymo told me about the opportunity\nAcademic Contact (professor recommendation, classmate, etc.)\nAgency Recruiter Outreach\nAlphabet's Internal Job Board (Grow)\nGlassdoor\nHandshake\nIndeed\nLinkedIn\nMedia (blog post, news article, etc.)\nSocial Media (Facebook, Twitter, etc.)\nUniversity Event (info session, tech talk, luncheon, etc.)\nUniversity Job Board\nWaymo Careers Page\nWaymo Industry Event (conference, career fair, etc.)\nWaymo Recruiter Outreach\nWord of Mouth\nOther\nI am authorized to work for any employer in the country in which this position is based.\nI require, or in the future will require, Waymo's sponsorship to obtain work authorization in the country in which this position is based (e.g. H-1B, TN, etc.)\nMy status to work in the country in which this position is based is unknown.\nI acknowledge that I have read and understood the terms of the Waymo Applicant and Candidate Privacy Policy.\nCurrent Alphabet Employee or Intern\nFormer Alphabet Employee or Intern\nCurrent or Former member of Alphabet extended workforce\nNever worked at Alphabet", "type": "", "href": "", "class": "call-to-action apply_url-call-to-action", "id": "", "data_qa": "", "visible": true, "tag_name": "div"}, {"text": "By submitting your email address, you agree to our Candidate Privacy Policy. The data you enter will be retained for two years (four years in California), unless you delete it before that time. You may unsubscribe from job alerts or remove your data from the system at any time.", "type": "", "href": "", "class": "call-to-action form-call-to-action", "id": "", "data_qa": "", "visible": true, "tag_name": "div"}], "file_inputs": [{"name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": false, "tag_name": "input", "type": "file"}, {"name": "form_submission[fields_attributes][4][job_questions][1c2f1d08be9b296bd7dd6b05ed4d6fc5]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": false, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions resume file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions cover-letter file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__item-cv", "accept": "", "multiple": false, "class": "iti__country ", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item dropdown", "visible": true, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-menu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "container dropdown-container", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "col-lg-5 col-12 first-col-dropdown", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "row dropdown-submenu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-item", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item pt-3 dropdown", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "navbarDropdown", "accept": "", "multiple": false, "class": "nav-link dropdown-toggle", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "job-component-icon-and-text job-component-dropdown-field-1", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown multi", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown-content", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "iti iti--allow-dropdown iti--show-flags iti--inline-dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__dropdown-content", "accept": "", "multiple": false, "class": "iti__dropdown-content iti__hide ", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958832 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958833 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958834 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958835 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "modal-backdrop show", "visible": true, "tag_name": "div", "type": ""}], "text_inputs": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][cb17eaabcb24317cee5894a18a7ae510]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][557d36d5c53d3670b3d66be2dec97909]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}], "links": [{"text": "Software Engineer, Linux Applications\nWarsaw, Masovian Voivodeship", "href": "https://careers.withwaymo.com/jobs/software-engineer-linux-applications-warsaw-masovian-voivodeship-poland", "class": ""}, {"text": "Search jobs", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn"}], "page_text": "  We prefer:\nExperience building data visualization applications with an eye for design.\nExperience with engineering artifacts, reliability monitoring and alerting, security and privacy practices and techniques, documentation, integration testing, production hygiene, and support processes.\nExperience communicating updates and resolutions with other software developers and cross-functional stakeholders at multiple levels and across sites.\n  #LI-Hybrid\nWe appreciate your interest in Waymo. Waymo is an equal employment opportunity employer, committed to maintaining a supportive and inclusive workplace for all employees. Waymo does not discriminate against, and prohibits harassment of, any applicant or employee based on race, color, sex, sexual orientation, gender identity, religion, national origin, age, disability, military status, genetic information or any other basis protected by applicable law. Waymo will also consider for employment qualified applicants with criminal records in accordance with applicable law. Waymo is committed to ensuring equal opportunity for qualified individuals with disabilities. If you are an individual with a disability and require an accommodation to participate in the application or interview process, please let the recruiting team know <NAME_EMAIL>. (This email address is intended to be used only for requesting accommodations as part of the application process. Other inquiries will not receive a response.)\nBy submitting your email address, you agree to our Candidate Privacy Policy. The data you enter will be retained for two years (four years in California), unless you delete it before that time. You may unsubscribe from job alerts or remove your data from the system at any time.\nRelated Job Openings\nBackend Software Engineer\nWarsaw, Masovian Voivodeship\nSoftware Engineer, Linux Applications\nWarsaw, Masovian Voivodeship\nData Engineer\nWarsaw, Masovian Voivodeship\nSoftware Engineer, Analysis Infrastructure\nMo", "current_url": "https://careers.withwaymo.com/jobs/fullstack-engineer-waymo-applications-and-tools-warsaw-masovian-voivodeship-poland-b0c52954-22a5-4b3a-b71c-e43400f2db79?gh_jid=6508003", "has_dynamic_content": true}, "application_analysis": {"application_type": "direct_form", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": false, "apply_button_found": false, "required_fields": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "placeholder": "Phone Number"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "placeholder": ""}], "file_upload_fields": [{"name": "form_submission[fields_attributes][4][job_questions][27e57f66d8c370bcc939be9760a77afd]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": false, "tag_name": "input", "type": "file"}, {"name": "form_submission[fields_attributes][4][job_questions][1c2f1d08be9b296bd7dd6b05ed4d6fc5]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": false, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions resume file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions cover-letter file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__item-cv", "accept": "", "multiple": false, "class": "iti__country ", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item dropdown", "visible": true, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-menu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "container dropdown-container", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "col-lg-5 col-12 first-col-dropdown", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "row dropdown-submenu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-item", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item pt-3 dropdown", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "navbarDropdown", "accept": "", "multiple": false, "class": "nav-link dropdown-toggle", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "job-component-icon-and-text job-component-dropdown-field-1", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown multi", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown-content", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "iti iti--allow-dropdown iti--show-flags iti--inline-dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__dropdown-content", "accept": "", "multiple": false, "class": "iti__dropdown-content iti__hide ", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958832 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958833 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958834 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-52958835 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "modal-backdrop show", "visible": true, "tag_name": "div", "type": ""}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly"]}, "timestamp": 1751871019.138758}, {"url": "https://boards.greenhouse.io/coveoen/jobs/7944459002", "page_info": {"title": "Software Developer - Machine Learning | Montreal (Province of Quebec, Canada) | Coveo", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002", "method": "get", "class": "", "inputs": [{"type": "hidden", "name": "MarketoDetailIDUrl", "id": "MarketoDetailIDUrl", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "application_number", "id": "application_number", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmadgroup", "id": "utmadgroup", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utm_campaign", "id": "utm_campaign", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utm_channel", "id": "utm_channel", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmcontent", "id": "utmcontent", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utm_medium", "id": "utm_medium", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmsource", "id": "utmsource", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "utmterm", "id": "utmterm", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "mapped_url_token", "id": "mapped_url_token", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "first_name", "id": "first_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "last_name", "id": "last_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "email", "id": "email", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "phone", "id": "phone", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "location", "id": "location", "placeholder": "", "required": true, "class": " "}, {"type": "file", "name": "resume_content", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "resume_content_validator", "id": "", "placeholder": "", "required": false, "class": "validateFile"}], "textareas": [{"name": "resume_text", "id": "resume", "placeholder": "", "required": true, "class": "materialize-textarea  "}, {"name": "question_32237734002", "id": "question_32237734002", "placeholder": "", "required": true, "class": "materialize-textarea  "}]}, {"index": 1, "action": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002", "method": "get", "class": "", "inputs": [{"type": "email", "name": "Email", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "FirstName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "LastName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Country", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "State", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Phone", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Title", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Company", "id": "", "placeholder": "", "required": false, "class": ""}], "textareas": []}], "buttons": [{"text": "I don't check all the boxes. Should I still apply?", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-0", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "What is Cove<PERSON>?", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-1", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "What is it like to work at Coveo?", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-2", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Work Modes: In-Person, Hybrid and Remote", "type": "submit", "href": "", "class": "cv-accordion-button", "id": "cv-dropdowns-with-media-heading-3", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Submit Application", "type": "submit", "href": "", "class": " button primary ", "id": "submit_application", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Close", "type": "submit", "href": "", "class": "onetrust-close-btn-handler onetrust-close-btn-ui banner-close-button ot-close-icon", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Close", "type": "submit", "href": "", "class": "ot-close-icon", "id": "close-pc-btn-handler", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Vendor Details button opens Vendor List menu", "type": "submit", "href": "", "class": "ot-link-btn category-host-list-handler", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Back", "type": "submit", "href": "", "class": "ot-link-btn back-btn-handler", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Filter", "type": "submit", "href": "", "class": "", "id": "filter-btn-handler", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Confirm my choices", "type": "submit", "href": "", "class": "save-preference-btn-handler onetrust-close-btn-handler", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Search", "type": "", "href": "", "class": "CoveoSearchButton coveo-accessible-button", "id": "", "data_qa": "", "visible": false, "tag_name": "a"}, {"text": "Contact Us", "type": "", "href": "https://www.coveo.com/en/contact", "class": "button simple ", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Pricing", "type": "", "href": "https://www.coveo.com/en/pricing", "class": "button simple ", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Apply", "type": "", "href": "", "class": "apply-container", "id": "", "data_qa": "", "visible": true, "tag_name": "div"}, {"text": "Clear", "type": "", "href": "", "class": "magic-box-clear coveo-accessible-button", "id": "", "data_qa": "", "visible": false, "tag_name": "div"}], "file_inputs": [{"name": "resume_content", "id": "", "accept": "", "multiple": false, "class": "", "visible": false, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload validateFile ", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-box", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-box-label active", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-caption", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-caption-default visible", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-file-info", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-file-name", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-file-size", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-clear-file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-clear-file-button", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-loading", "visible": false, "tag_name": "div", "type": ""}, {"name": "resume_text", "id": "resume", "accept": "", "multiple": false, "class": "materialize-textarea  ", "visible": false, "tag_name": "textarea", "type": "textarea"}, {"name": "", "id": "cv-dropdowns-with-media-heading-0", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-0", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "cv-dropdowns-with-media-heading-1", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-1", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "cv-dropdowns-with-media-heading-2", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-2", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "cv-dropdowns-with-media-heading-3", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-3", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": false, "tag_name": "div", "type": ""}, {"name": "resume", "id": "", "accept": "", "multiple": false, "class": "", "visible": true, "tag_name": "fieldset", "type": "fieldset"}, {"name": "resume_content_validator", "id": "", "accept": "", "multiple": false, "class": "validateFile", "visible": false, "tag_name": "input", "type": "text"}, {"name": "", "id": "_9B8D4582-D238-453D-BCA6-F77213CE5779_container", "accept": "", "multiple": false, "class": "coveo-dropdown-searchbox-section CoveoSearchInterface Coveostate CoveoComponentState CoveoComponentOptions CoveoQueryController CoveoDebug coveo-after-initialization", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "coveo-dropdown-tab-section", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "coveo-dropdown-tab-component-container", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "coveo-dropdown-tab-debug-information coveo-debug-information coveo-debug-hidden", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "cv-accordion-block cv-dropdowns-with-media-accordion-block cv-accordion-block-expanded", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "cv-accordion-block cv-dropdowns-with-media-accordion-block", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "select-dropdown", "visible": true, "tag_name": "input", "type": "text"}, {"name": "", "id": "select-options-8ee7639f-36f2-26e4-edcd-8df291239d09", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-e94a6cb3-e41e-6f2a-727d-ee83b77f1288", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-3bcc0072-aa98-589f-4307-d9b31367e111", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-60b0d3a6-3d62-bb82-49fd-6a6b19d2aefc", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-fd401c6c-b454-f3b8-026c-74095204451b", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-63d6390f-b819-fd4d-9040-5006b9004fe9", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-7ac44263-a7ed-0b8e-142b-52a2a15eb77e", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-50684a14-846a-6be1-df3b-20ffb00e92b5", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}], "text_inputs": [{"type": "text", "name": "", "id": "", "placeholder": "What are you looking for?", "required": false, "class": ""}, {"type": "text", "name": "first_name", "id": "first_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "last_name", "id": "last_name", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "email", "id": "email", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "phone", "id": "phone", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "location", "id": "location", "placeholder": "", "required": true, "class": " "}, {"type": "text", "name": "resume_content_validator", "id": "", "placeholder": "", "required": false, "class": "validateFile"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": false, "class": "select-dropdown"}, {"type": "text", "name": "FirstName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "LastName", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Country", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "State", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Phone", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Title", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "Company", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "vendor-search-handler", "id": "vendor-search-handler", "placeholder": "Search…", "required": false, "class": ""}, {"type": "email", "name": "Email", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "textarea", "name": "resume_text", "id": "resume", "placeholder": "", "required": true, "class": "materialize-textarea  "}, {"type": "textarea", "name": "question_32237734002", "id": "question_32237734002", "placeholder": "", "required": true, "class": "materialize-textarea  "}], "links": [{"text": "careers", "href": "https://www.coveo.com/en/company/careers", "class": "href-container"}, {"text": "Our job openings", "href": "https://www.coveo.com/en/company/careers/open-positions", "class": "trigger-submenu"}, {"text": "Apply", "href": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002#job-form", "class": "primary-white-large"}, {"text": "Careers", "href": "https://www.coveo.com/en/company/careers", "class": "menu-link "}], "page_text": "careers\nCoveo.com\nHome\nOur job openings\nOur culture\nOur Team\nInternships\nSoftware Developer - Machine Learning\nMontreal (Province of Quebec, Canada)\nMachine Learning\nFull-time\nRemote or Hybrid\nApply\n< See All Positions\n  Enable NLP technology reuse across the company\nAs a Software Developer in Machine Learning, you will play a key role in supporting teams of applied scientists and ML developers who train, evaluate, and use a variety of NLP models, including LLMs.\nYour team is the CoreNLP team, a mix of scientists and developers who provide reusable NLP technologies that accelerate delivery for the other teams in your unit. Your mission is to contribute to prototyping, productionizing, and maintaining the NLP technologies that power some of Coveo's most visible AI capabilities.\nHere is what makes this opportunity exciting:\nYour team is uniquely positioned to impact Coveo’s research and development efforts and offers one of the best environments to quickly get up to speed with state-of-the-art NLP technology.\nThe ML unit at Coveo focuses on finding ways to apply the latest advances in Recommender Systems, Ranking Optimization, LLMs and NLP to build innovative solutions in commerce, self-service and other business verticals. We solve real problems with real data, for hundreds of large enterprise clients all around the world, on a modern platform that serves over 100M requests and automatically trains thousands of ML models on a daily basis. \nHere is a glimpse at your responsibilities:\nParticipate directly in every aspect of NLP technology delivery: requirements gathering, conception, implementation, automated testing, release, monitoring, maintenance, etc.\nAlong with the rest of your team, make continuous learning a weekly practice that ensures awareness of emerging opportunities in the field.\nIdentify current pain points in NLP research and development and deliver software that addresses them.\nEngage with your community of peers to challenge the status quo, improve ou", "current_url": "https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002", "has_dynamic_content": true}, "application_analysis": {"application_type": "external_redirect", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": true, "apply_button_found": true, "required_fields": [{"type": "text", "name": "first_name", "placeholder": ""}, {"type": "text", "name": "last_name", "placeholder": ""}, {"type": "text", "name": "email", "placeholder": ""}, {"type": "text", "name": "phone", "placeholder": ""}, {"type": "text", "name": "location", "placeholder": ""}], "file_upload_fields": [{"name": "resume_content", "id": "", "accept": "", "multiple": false, "class": "", "visible": false, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload validateFile ", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-box", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-box-label active", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-caption", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-caption-default visible", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-file-info", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-file-name", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-file-size", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-clear-file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-clear-file-button", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload-loading", "visible": false, "tag_name": "div", "type": ""}, {"name": "resume_text", "id": "resume", "accept": "", "multiple": false, "class": "materialize-textarea  ", "visible": false, "tag_name": "textarea", "type": "textarea"}, {"name": "", "id": "cv-dropdowns-with-media-heading-0", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-0", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "cv-dropdowns-with-media-heading-1", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-1", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "cv-dropdowns-with-media-heading-2", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-2", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "cv-dropdowns-with-media-heading-3", "accept": "", "multiple": false, "class": "cv-accordion-button", "visible": true, "tag_name": "button", "type": "submit"}, {"name": "", "id": "cv-dropdowns-with-media-content-3", "accept": "", "multiple": false, "class": "cv-accordion-body", "visible": false, "tag_name": "div", "type": ""}, {"name": "resume", "id": "", "accept": "", "multiple": false, "class": "", "visible": true, "tag_name": "fieldset", "type": "fieldset"}, {"name": "resume_content_validator", "id": "", "accept": "", "multiple": false, "class": "validateFile", "visible": false, "tag_name": "input", "type": "text"}, {"name": "", "id": "_9B8D4582-D238-453D-BCA6-F77213CE5779_container", "accept": "", "multiple": false, "class": "coveo-dropdown-searchbox-section CoveoSearchInterface Coveostate CoveoComponentState CoveoComponentOptions CoveoQueryController CoveoDebug coveo-after-initialization", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "coveo-dropdown-tab-section", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "coveo-dropdown-tab-component-container", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "coveo-dropdown-tab-debug-information coveo-debug-information coveo-debug-hidden", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "cv-accordion-block cv-dropdowns-with-media-accordion-block cv-accordion-block-expanded", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "cv-accordion-block cv-dropdowns-with-media-accordion-block", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "select-dropdown", "visible": true, "tag_name": "input", "type": "text"}, {"name": "", "id": "select-options-8ee7639f-36f2-26e4-edcd-8df291239d09", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-e94a6cb3-e41e-6f2a-727d-ee83b77f1288", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-3bcc0072-aa98-589f-4307-d9b31367e111", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-60b0d3a6-3d62-bb82-49fd-6a6b19d2aefc", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-fd401c6c-b454-f3b8-026c-74095204451b", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-63d6390f-b819-fd4d-9040-5006b9004fe9", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-7ac44263-a7ed-0b8e-142b-52a2a15eb77e", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "select-options-50684a14-846a-6be1-df3b-20ffb00e92b5", "accept": "", "multiple": false, "class": "dropdown-content select-dropdown ", "visible": false, "tag_name": "ul", "type": ""}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly", "Application requires redirect to: https://www.coveo.com/en/company/careers/open-positions/research-and-development/machine-learning/software-developer-machine-learning/7944459002?gh_jid=7944459002#job-form"]}, "timestamp": 1751871064.202477}, {"url": "https://boards.greenhouse.io/waymo/jobs/6885682", "page_info": {"title": "Design Engineer, ASIC - Mountain View, California, United States", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://careers.withwaymo.com/call_to_actions/36ee3fafcbec8a9d447913670f178bc5/form_submissions?job_id=25855ed77e829b9ed610f2db8988a8f0&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_1_3_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_1_3_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_1_3_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_job_alert_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_1_3_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_departments_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "form_locations_1_3-selectized", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_1_3", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 1, "action": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_1_3", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}, {"index": 2, "action": "https://careers.withwaymo.com/call_to_actions/4e392923843c0621f469608bfdf96a90/form_submissions?job_id=25855ed77e829b9ed610f2db8988a8f0&page_id=f51bb99633e97b1b6a2e33fe11657de6", "method": "post", "class": "form-template", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "exit_call_to_action_configuration_id", "id": "exit_call_to_action_configuration_id_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission_hp", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][0][kind]", "id": "form_field_kind_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][0][form_template_field_uid]", "id": "form_form_template_field_id_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][1][kind]", "id": "form_field_kind_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][1][form_template_field_uid]", "id": "form_form_template_field_id_7_0_1", "placeholder": "", "required": false, "class": ""}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][2][kind]", "id": "form_field_kind_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][2][form_template_field_uid]", "id": "form_form_template_field_id_7_0_2", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}, {"type": "hidden", "name": "form_submission[fields_attributes][3][kind]", "id": "form_field_kind_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][3][form_template_field_uid]", "id": "form_form_template_field_id_7_0_3", "placeholder": "", "required": false, "class": ""}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "id": "question_7_0_4_0_0", "placeholder": "", "required": true, "class": "form-control"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][22037104a68ba0da1dcc90fd9f0456e1]", "id": "question_7_0_4_0_1", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][a406c0b96a2da3d8fb0842bdca990c39]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][65d22f5f7e35bafcd6ce2209cabf1456]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "hidden", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "form_submission_fields_attributes_4_job_questions_bbd797064c388ae2fbd3e97fbccc267c_job_question_option_uid_", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_5e8501b92b8024b3bb3b8a07d11520cb", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_39be16ebb368cef9f62a585ecb7fa9d8", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_6d0c84c3ab5a293d35c712530c78785b", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_b23402f513f3384c2311a00b61563cda", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_9efd329f738608c27a829bd6ce2cddf9", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_7e588b223f7770c7bd4e896904760473", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][4][job_questions][bbd797064c388ae2fbd3e97fbccc267c][job_question_option_uid][]", "id": "question_7_0_4_3_1_option_e7ad7d0352e4dcedd00960b3da9ab52b", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][4][kind]", "id": "form_field_kind_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][4][form_template_field_uid]", "id": "form_form_template_field_id_7_0_4", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_job_alert_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][kind]", "id": "form_field_kind_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][form_template_field_uid]", "id": "form_form_template_field_id_7_0_5", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][agreement]", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "checkbox", "name": "form_submission[fields_attributes][5][agreement]", "id": "form_agreement_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][department_uids][]", "id": "form_departments_7_0_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "form_submission[fields_attributes][5][locations_json][]", "id": "form_locations_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "_empty", "id": "empty_field_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "candidate_referral_uid", "id": "candidate_referral_uid_7_0", "placeholder": "", "required": false, "class": ""}], "textareas": []}, {"index": 3, "action": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#", "method": "post", "class": "", "inputs": [{"type": "hidden", "name": "authenticity_token", "id": "", "placeholder": "", "required": false, "class": ""}, {"type": "hidden", "name": "pass_through", "id": "pass_through_verify_7_0", "placeholder": "", "required": false, "class": ""}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}], "textareas": []}], "buttons": [{"text": "Toggle navigation", "type": "button", "href": "", "class": "navbar-toggler collapsed", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Share", "type": "submit", "href": "", "class": "IN-e146f606-483d-4824-a598-79a778fddfb4-1G9ISYhSF8XoOmdcl0yKDu", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Share", "type": "submit", "href": "", "class": "facebook-share-button", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Notify me", "type": "submit", "href": "", "class": "btn btn-primary", "id": "form_submit_1_3", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Previous", "type": "button", "href": "", "class": "carousel-control-prev", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Next", "type": "button", "href": "", "class": "carousel-control-next", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Ethiopia +251", "type": "button", "href": "", "class": "iti__selected-country", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Submit your application", "type": "submit", "href": "", "class": "btn btn-primary", "id": "form_submit_7_0", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Close", "type": "button", "href": "", "class": "btn-close", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Add to favorites", "type": "", "href": "", "class": "btn btn-primary candidate-favourite", "id": "link_candidate_favourite_1_2", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "View favorites", "type": "", "href": "https://careers.withwaymo.com/me/settings", "class": "btn btn-primary view-favourites", "id": "link_view_favourites_1_2", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Search jobs", "type": "", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Apply now", "type": "", "href": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#apply", "class": "button button4", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Learn more", "type": "", "href": "https://careers.withwaymo.com/benefits", "class": "button button2", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "To ensure thorough consideration of each application, candidates may submit up to 5 job applications within a 60-day period. We encourage you to carefully review job descriptions and apply to positions that best match your skills. Applications beyond this limit will not be considered.\nFirst Name (required)\nLast Name (required)\nEmail (required)\nPhone Number (required)\nEthiopia +251\nResume (required)\nCover Letter\nWebsite\nLinkedIn Profile\nHow did you hear about this opportunity?\nA personal contact at Google/Alphabet told me about the opportunity\nA personal contact at Waymo told me about the opportunity\nAcademic Contact (professor recommendation, classmate, etc.)\nAgency Recruiter Outreach\nAlphabet's Internal Job Board (Grow)\nGlassdoor\nHandshake\nIndeed\nLinkedIn\nMedia (blog post, news article, etc.)\nSocial Media (Facebook, Twitter, etc.)\nUniversity Event (info session, tech talk, luncheon, etc.)\nUniversity Job Board\nWaymo Careers Page\nWaymo Industry Event (conference, career fair, etc.)\nWaymo Recruiter Outreach\nWord of Mouth\nOther\nWork Authorization (required)\nNote: Upon hire, all employees are required to submit proof of legal right to work, consistent with applicable law. <PERSON><PERSON> also participates in the E-Verify program.\nI am authorized to work for any employer in the country in which this position is based.\nI require, or in the future will require, <PERSON><PERSON>'s sponsorship to obtain work authorization in the country in which this position is based (e.g. H-1B, TN, etc.)\nMy status to work in the country in which this position is based is unknown.\nPlease review and acknowledge our Candidate Privacy Policy linked below: (required)\nWaymo Applicant and Candidate Privacy Policy\nI acknowledge that I have read and understood the terms of the Waymo Applicant and Candidate Privacy Policy.\nAre you a current or former Alphabet employee (including Google and other Alphabet subsidiaries)? (required)\nCurrent Alphabet Employee or Intern\nFormer Alphabet Employee or Intern\nCurrent or Former member of Alphabet extended workforce\nNever worked at Alphabet\nDemographic Information (completion voluntary)\nWaymo would greatly appreciate it if you could provide us with your identity information. Your individual identity data will not be accessible to recruiters, hiring managers or others who interview you. Instead, it is used in aggregate to track and report Equity, Inclusion, and Diversity (EID) progress. Answering these questions is entirely voluntary, and a decision not to answer will not be held against you. \nIf you decide to provide demographic information, it will be recorded and maintained in a confidential file. Whatever your decision about providing this data, it will not be considered in the hiring process or as a basis for employment decisions. If you are offered and accept employment with Waymo, the information collected during the application and recruitment process will become part of your confidential employment record.\nGender (required)\nMan\nWoman\nNon-Binary\nI don't wish to answer\nRace/Ethnicity (Select all that apply) (required)\nAmerican Indian or Alaska Native\nAsian\nBlack or African American\nHispanic or Latino\nNative Hawaiian or Other Pacific Islander\nWhite\nI don't wish to answer\nAre you a veteran or reservist? (required)\nYes\nNo\nI don't wish to answer\nDo you identify as a member of the LGBTQ+ (e.g., lesbian, gay, bisexual, queer, asexual) community? (required)\nYes\nNo\nI don't wish to answer\nCheck this box to join the talent community and sign up for job alerts\nSubmit your application\nBy submitting your email address, you agree to our Candidate Privacy Policy. The data you enter will be retained for two years (four years in California), unless you delete it before that time. You may unsubscribe from job alerts or remove your data from the system at any time.", "type": "", "href": "", "class": "call-to-action apply_url-call-to-action", "id": "", "data_qa": "", "visible": true, "tag_name": "div"}, {"text": "Job Alerts\nSign up to receive tailored job alerts delivered straight to your inbox!\nFirst Name\nLast Name\nEmail\nChoose departments and locations you're interested in:\nDepartments\nDepartments\nHARDWARE ENGINEERING\nLocations\nLocations\nMOUNTAIN VIEW, CALIFORNIA, UNITED STATES\nNotify me\nBy submitting your email address, you agree to our Candidate Privacy Policy. The data you enter will be retained for two years (four years in California), unless you delete it before that time. You may unsubscribe from job alerts or remove your data from the system at any time.", "type": "", "href": "", "class": "call-to-action form-call-to-action", "id": "", "data_qa": "", "visible": true, "tag_name": "div"}], "file_inputs": [{"name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": true, "tag_name": "input", "type": "file"}, {"name": "form_submission[fields_attributes][4][job_questions][22037104a68ba0da1dcc90fd9f0456e1]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": true, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions resume file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions cover-letter file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__item-cv", "accept": "", "multiple": false, "class": "iti__country ", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item dropdown", "visible": true, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-menu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "container dropdown-container", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "col-lg-5 col-12 first-col-dropdown", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "row dropdown-submenu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-item", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item pt-3 dropdown", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "navbarDropdown", "accept": "", "multiple": false, "class": "nav-link dropdown-toggle", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "job-component-icon-and-text job-component-dropdown-field-1", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown multi", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown-content", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "iti iti--allow-dropdown iti--show-flags iti--inline-dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__dropdown-content", "accept": "", "multiple": false, "class": "iti__dropdown-content iti__hide ", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674973 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674974 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674975 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674976 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions 1570 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions 1572 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions 1573 dropdown", "visible": true, "tag_name": "div", "type": ""}], "text_inputs": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_1_3_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_1_3_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_1_3", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "id": "form_first_name_7_0_0", "placeholder": "First Name", "required": true, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "id": "form_last_name_7_0_1", "placeholder": "Last Name", "required": true, "class": "form-control"}, {"type": "text", "name": "", "id": "", "placeholder": "Search", "required": false, "class": "iti__search-input"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][a406c0b96a2da3d8fb0842bdca990c39]", "id": "question_7_0_4_0_2", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "form_submission[fields_attributes][4][job_questions][65d22f5f7e35bafcd6ce2209cabf1456]", "id": "question_7_0_4_0_3", "placeholder": "", "required": false, "class": "form-control"}, {"type": "text", "name": "code", "id": "call_to_action_verify_code_7_0", "placeholder": "", "required": false, "class": "form-control call-to-action-code"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_1_3_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "id": "form_email_7_0_2", "placeholder": "Email", "required": true, "class": "form-control"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "id": "form_phone_number_7_0_3", "placeholder": "Phone Number", "required": true, "class": "form-control iti__tel-input"}], "links": [{"text": "Early careers", "href": "https://careers.withwaymo.com/early-careers", "class": "nav-link"}, {"text": "Apply now", "href": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#apply", "class": "button button4"}, {"text": "Search jobs", "href": "https://careers.withwaymo.com/jobs/search", "class": "btn btn-primary jobs-btn"}], "page_text": "Why <PERSON><PERSON>\nWorking at Waymo\nTeams\nEarly careers\nHow we hire\nOpen roles\nVisit Waymo.com\nDesign Engineer, ASIC\nMOUNTAIN VIEW, CALIFORNIA, UNITED STATES FULL-TIME HARDWARE ENGINEERING 3496\nApply now\nAdd to favorites View favorites\nWaymo is an autonomous driving technology company with the mission to be the most trusted driver. Since its start as the Google Self-Driving Car Project in 2009, Waymo has focused on building the Waymo Driver—The World's Most Experienced Driver™—to improve access to mobility while saving thousands of lives now lost to traffic crashes. The Waymo Driver powers Waymo One, a fully autonomous ride-hailing service, and can also be applied to a range of vehicle platforms and product use cases. The Waymo Driver has provided over one million rider-only trips, enabled by its experience autonomously driving tens of millions of miles on public roads and tens of billions in simulation across 13+ U.S. states.\nWaymo's Compute Team is tasked with a critical and exciting mission: We deliver the compute platform responsible for running the fully autonomous vehicle's software stack. To achieve our mission, we architect and create high-performance custom silicon; we develop system-level compute architectures that push the boundaries of performance, power, and latency; and we collaborate closely with many other teammates to ensure we design and optimize hardware and software for maximum performance. We are a multidisciplinary team seeking curious and talented teammates to work on one of the world's highest performance automotive compute platforms.\nIn this hybrid role, you'll report to an ASIC Design Manager\nYou will:\nWork with researchers and architects to translate high level requirements into hardware features\nSpecify, architect, and design hardware accelerators that process sensor data and other system compute workloads\nPerform power, area and performance trade-off analyses of digital designs\nWork with verification teams to guarantee functional correctness and ", "current_url": "https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682", "has_dynamic_content": true}, "application_analysis": {"application_type": "external_redirect", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": true, "apply_button_found": true, "required_fields": [{"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "text", "name": "form_submission[fields_attributes][0][string_value]", "placeholder": "First Name"}, {"type": "text", "name": "form_submission[fields_attributes][1][string_value]", "placeholder": "Last Name"}, {"type": "email", "name": "form_submission[fields_attributes][2][email_value]", "placeholder": "Email"}, {"type": "tel", "name": "form_submission[fields_attributes][3][phone_value]", "placeholder": "Phone Number"}, {"type": "file", "name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "placeholder": ""}], "file_upload_fields": [{"name": "form_submission[fields_attributes][4][job_questions][212f01d94d5d2f417f7888f97692c496]", "id": "question_7_0_4_0_0", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": true, "tag_name": "input", "type": "file"}, {"name": "form_submission[fields_attributes][4][job_questions][22037104a68ba0da1dcc90fd9f0456e1]", "id": "question_7_0_4_0_1", "accept": "application/pdf,.doc,.docx,.xml,application/msword,application/rtf,text/rtf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text", "multiple": false, "class": "form-control", "visible": true, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions resume file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions cover-letter file", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__item-cv", "accept": "", "multiple": false, "class": "iti__country ", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item dropdown", "visible": true, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-menu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "container dropdown-container", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "col-lg-5 col-12 first-col-dropdown", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "row dropdown-submenu", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "dropdown-item", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "nav-item pt-3 dropdown", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "navbarDropdown", "accept": "", "multiple": false, "class": "nav-link dropdown-toggle", "visible": false, "tag_name": "a", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "job-component-icon-and-text job-component-dropdown-field-1", "visible": false, "tag_name": "li", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown multi", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "selectize-dropdown-content", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "iti iti--allow-dropdown iti--show-flags iti--inline-dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "iti-0__dropdown-content", "accept": "", "multiple": false, "class": "iti__dropdown-content iti__hide ", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674973 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674974 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674975 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions question-56674976 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions 1570 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions 1572 dropdown", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "form-group application-questions 1573 dropdown", "visible": true, "tag_name": "div", "type": ""}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly", "Application requires redirect to: https://careers.withwaymo.com/jobs/design-engineer-asic-mountain-view-california-united-states?gh_jid=6885682#apply"]}, "timestamp": 1751871082.794346}, {"url": "http://job-boards.greenhouse.io/nerdy/jobs/7045082", "page_info": {"title": "Job Application for Director of Supply Operations at Nerdy", "company": "", "location": "", "description": "", "forms": [{"index": 0, "action": "https://job-boards.greenhouse.io/nerdy/jobs/7045082", "method": "get", "class": "application--form", "inputs": [{"type": "text", "name": "", "id": "first_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "last_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "preferred_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "email", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "phone", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "file", "name": "", "id": "resume", "placeholder": "", "required": false, "class": "visually-hidden"}, {"type": "file", "name": "", "id": "cover_letter", "placeholder": "", "required": false, "class": "visually-hidden"}, {"type": "text", "name": "", "id": "question_58292628", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292629", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292630", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292631", "placeholder": "", "required": false, "class": "select__input"}, {"type": "text", "name": "", "id": "", "placeholder": "", "required": true, "class": "remix-css-1a0ro4n-requiredInput"}, {"type": "text", "name": "", "id": "question_58292632", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292633", "placeholder": "", "required": false, "class": "input input__single-line"}], "textareas": []}], "buttons": [{"text": "Apply", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Autofill with Greenhouse", "type": "button", "href": "", "class": "btn btn--pill btn--secondary", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Attach", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Dropbox", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Google Drive", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Enter manually", "type": "button", "href": "", "class": "btn btn--pill", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Toggle flyout", "type": "button", "href": "", "class": "icon-button icon-button--sm", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Submit application", "type": "submit", "href": "", "class": "btn btn--pill", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}], "file_inputs": [{"name": "", "id": "resume", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden", "visible": true, "tag_name": "input", "type": "file"}, {"name": "", "id": "cover_letter", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden", "visible": true, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "upload-label-resume", "accept": "", "multiple": false, "class": "label upload-label", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload__wrapper", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "accepted-filetypes", "accept": "", "multiple": false, "class": "file-upload__filetypes", "visible": true, "tag_name": "p", "type": ""}, {"name": "", "id": "upload-label-cover_letter", "accept": "", "multiple": false, "class": "label upload-label", "visible": true, "tag_name": "div", "type": ""}], "text_inputs": [{"type": "text", "name": "", "id": "first_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "last_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "preferred_name", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "email", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "phone", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292628", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292629", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292630", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292631", "placeholder": "", "required": false, "class": "select__input"}, {"type": "text", "name": "", "id": "question_58292632", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "text", "name": "", "id": "question_58292633", "placeholder": "", "required": false, "class": "input input__single-line"}, {"type": "textarea", "name": "g-recaptcha-response", "id": "g-recaptcha-response-100000", "placeholder": "", "required": false, "class": "g-recaptcha-response"}], "links": [], "page_text": "New\nDirector of Supply Operations\nRemote - Dallas\nApply\nOverview:\n<PERSON><PERSON><PERSON> is seeking a Director of Supply Operations to lead and optimize the supply-side marketplace operations for our consumer business. The Director will own operational processes around tutor forecasting, sourcing, subject-matter vetting, and marketplace excellence, leveraging automation and AI to ensure our tutor supply aligns seamlessly with client demand and to drive continuous improvement as we scale.\nThis role is strategically vital for scaling operations, maintaining marketplace health, and driving rapid growth across our learning platform. We are seeking a strategically-minded, analytically rigorous leader—someone who thrives in marketplace operations, excels at forecasting and supply optimization, and can independently dive into data, develop operational models, and collaborate with Product and Engineering to prototype AI-based solutions. The ideal candidate has experience navigating data-rich environments and seamlessly translating strategy into hands-on execution.\nAbout Nerdy:\nAt Nerdy (NYSE: NRDY) -  the company behind Varsity Tutors - we’re redrawing the blueprint of learning. Our Live + AI™ platform fuses real-time human expertise with proprietary generative-AI systems, setting a new bar for measurable academic impact at global scale. \nWe recruit the kind of technologists and operators you’d bet on as solo founders - people who turn ambiguous problems into shipping code, iterate faster than markets move, and compound their advantage with every data point. In an era where great employees can deliver 10-times the leverage of the merely good, we back those who play to win.\nFortune favors the bold. Join us.\nHow we compete:\nAI-Native at every level\nFrom the CEO to day-one hires, everyone builds and ships with generative AI. If you’re not wielding AI, you’re not done.\nEntrepreneurial velocity\nMove at founder speed, prototype in hours, and measure in real user outcomes. Slow teams die.\nFree-mar", "current_url": "https://job-boards.greenhouse.io/nerdy/jobs/7045082", "has_dynamic_content": true}, "application_analysis": {"application_type": "direct_form", "has_direct_form": true, "has_file_upload": true, "requires_external_redirect": false, "apply_button_found": true, "required_fields": [{"type": "text", "name": "", "placeholder": ""}], "file_upload_fields": [{"name": "", "id": "resume", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden", "visible": true, "tag_name": "input", "type": "file"}, {"name": "", "id": "cover_letter", "accept": ".pdf,.doc,.docx,.txt,.rtf", "multiple": false, "class": "visually-hidden", "visible": true, "tag_name": "input", "type": "file"}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "upload-label-resume", "accept": "", "multiple": false, "class": "label upload-label", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "file-upload__wrapper", "visible": true, "tag_name": "div", "type": ""}, {"name": "", "id": "accepted-filetypes", "accept": "", "multiple": false, "class": "file-upload__filetypes", "visible": true, "tag_name": "p", "type": ""}, {"name": "", "id": "upload-label-cover_letter", "accept": "", "multiple": false, "class": "label upload-label", "visible": true, "tag_name": "div", "type": ""}], "application_steps": ["1. Fill out application form on current page", "2. Upload resume/CV file", "3. Submit application"], "recommendations": ["Page has file upload capability - can upload resume/CV", "Page has application form - can fill out directly", "✅ Greenhouse platform detected - standard application flow expected"], "platform": "greenhouse"}, "timestamp": 1751871101.350535}, {"url": "https://boards.greenhouse.io/squarespace/jobs/7036031", "page_info": {"title": "Sales Manager, Cross-Sell & Expansion – Careers – Squarespace", "company": "", "location": "", "description": "", "forms": [], "buttons": [{"text": "PRODUCTS", "type": "submit", "href": "", "class": "cta cta--inline global-navigation__header-link-item", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "RESOURCES", "type": "submit", "href": "", "class": "cta cta--inline global-navigation__header-link-item", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Account information dropdown menu", "type": "submit", "href": "", "class": "cta cta--inline cta--light global-navigation__account-avatar", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Navigation menu", "type": "submit", "href": "", "class": "cta cta--inline global-navigation__hamburger", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Select language:", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "Select currency:", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "USD", "type": "submit", "href": "", "class": "footer__locale-setting-option footer__locale-setting-option--is-active", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "EUR", "type": "submit", "href": "", "class": "footer__locale-setting-option", "id": "", "data_qa": "", "visible": false, "tag_name": "button"}, {"text": "English", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "$ USD", "type": "submit", "href": "", "class": "footer__locale-setting-button", "id": "", "data_qa": "", "visible": true, "tag_name": "button"}, {"text": "Squarespace homepage", "type": "", "href": "https://www.squarespace.com/", "class": "cta cta--inline global-navigation__logo-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "LOG IN", "type": "", "href": "https://www.squarespace.com/auth/protected-redirect/login?location=https%3A%2F%2Faccount.squarespace.com", "class": "cta cta--inline global-navigation__account-login global-navigation__account-login--is-visible", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "GET STARTED", "type": "", "href": "https://www.squarespace.com/templates/get-started", "class": "cta cta--primary global-navigation__cta global-navigation__default-cta", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Squarespace homepage", "type": "", "href": "https://www.squarespace.com/", "class": "cta cta--inline cta--light", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Website Templates", "type": "", "href": "https://www.squarespace.com/templates", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Websites", "type": "", "href": "https://www.squarespace.com/website-design", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Domains", "type": "", "href": "https://domains.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "AI Website Builder", "type": "", "href": "https://www.squarespace.com/websites/ai-website-builder", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Design Intelligence", "type": "", "href": "https://www.squarespace.com/design-intelligence", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Online Stores", "type": "", "href": "https://www.squarespace.com/online-stores", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Services", "type": "", "href": "https://www.squarespace.com/services", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Invoicing", "type": "", "href": "https://www.squarespace.com/invoicing", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Scheduling", "type": "", "href": "https://www.squarespace.com/scheduling", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Content & Memberships", "type": "", "href": "https://www.squarespace.com/content", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Donations", "type": "", "href": "https://www.squarespace.com/donations", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Payments", "type": "", "href": "https://www.squarespace.com/payments", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Marketing Tools", "type": "", "href": "https://www.squarespace.com/website-marketing", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Email Campaigns", "type": "", "href": "https://www.squarespace.com/email-marketing", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Professional Email", "type": "", "href": "https://www.squarespace.com/professional-email", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Feature List", "type": "", "href": "https://www.squarespace.com/feature-index", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Pricing", "type": "", "href": "https://www.squarespace.com/pricing", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Customer Examples", "type": "", "href": "https://www.squarespace.com/showcase", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Squarespace Collection", "type": "", "href": "https://collection.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Fitness", "type": "", "href": "https://www.squarespace.com/solutions/fitness", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Beauty", "type": "", "href": "https://www.squarespace.com/solutions/beauty", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Photography", "type": "", "href": "https://www.squarespace.com/tour/photography-websites", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Restaurants", "type": "", "href": "https://www.squarespace.com/tour/restaurant-websites", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Art & Design", "type": "", "href": "https://www.squarespace.com/websites/create-a-portfolio", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Wedding", "type": "", "href": "https://www.squarespace.com/tour/wedding-website", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Creators", "type": "", "href": "https://www.squarespace.com/creators", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Enterprise", "type": "", "href": "https://www.squarespace.com/enterprise", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "For Professionals", "type": "", "href": "https://www.squarespace.com/circle", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Affiliates", "type": "", "href": "https://www.squarespace.com/affiliates", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Help Center", "type": "", "href": "https://support.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Forum", "type": "", "href": "https://forum.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Webinars", "type": "", "href": "https://learning.squarespace.com/webinars", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "<PERSON><PERSON> an Expert", "type": "", "href": "https://www.squarespace.com/designer/home?channel=sqsp_circle&subchannel=frontsite_footer&utm_medium=sqsp_circle&utm_source=frontsite_footer", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Developer Blog", "type": "", "href": "https://engineering.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Developer Platform", "type": "", "href": "https://developers.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "System Status", "type": "", "href": "https://status.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Extensions", "type": "", "href": "https://www.squarespace.com/extensions/home", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Squarespace Blog", "type": "", "href": "https://www.squarespace.com/blog", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Free Tools", "type": "", "href": "https://www.squarespace.com/tools", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Business Name Generator", "type": "", "href": "https://www.squarespace.com/tools/business-name-generator", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Logo Maker", "type": "", "href": "https://www.squarespace.com/logo", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "About", "type": "", "href": "https://www.squarespace.com/about/company", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Careers", "type": "", "href": "https://www.squarespace.com/about/careers", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Our History", "type": "", "href": "https://timeline.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Our Brand", "type": "", "href": "https://brand.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Accessibility", "type": "", "href": "https://www.squarespace.com/accessibility", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Newsroom", "type": "", "href": "https://newsroom.squarespace.com/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Press & Media", "type": "", "href": "https://www.squarespace.com/press-coverage", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Contact Us", "type": "", "href": "https://www.squarespace.com/contact", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Instagram", "type": "", "href": "https://www.instagram.com/squarespace/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Youtube", "type": "", "href": "https://www.youtube.com/squarespace", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Linkedin", "type": "", "href": "https://www.linkedin.com/company/squarespace/", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Facebook", "type": "", "href": "https://www.facebook.com/squarespace", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "X", "type": "", "href": "https://x.com/squarespace", "class": "cta cta--inline cta--light footer__link-item-link", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Terms", "type": "", "href": "https://www.squarespace.com/terms-of-service", "class": "cta cta--inline cta--light", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Privacy", "type": "", "href": "https://www.squarespace.com/privacy", "class": "cta cta--inline cta--light", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Security Measures", "type": "", "href": "https://www.squarespace.com/security", "class": "cta cta--inline cta--light", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}, {"text": "Sitemap", "type": "", "href": "https://www.squarespace.com/sitemap.xml", "class": "cta cta--inline cta--light", "id": "", "data_qa": "", "visible": true, "tag_name": "a"}], "file_inputs": [{"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown-caret", "visible": false, "tag_name": "svg", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown-header", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "text text--footnote text--light global-navigation__account-dropdown-title", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "text text--footnote text--light global-navigation__account-dropdown-title global-navigation__account-email", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown-list", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "cta cta--inline cta--light global-navigation__account-dropdown-list-item", "visible": false, "tag_name": "a", "type": ""}], "text_inputs": [], "links": [{"text": "Careers", "href": "https://www.squarespace.com/about/careers", "class": "cta cta--inline cta--light footer__link-item-link"}], "page_text": "PRODUCTS\nRESOURCES\nLOG IN\nGET STARTED\nCHANNELS AND SERVICES\nSales Manager, Cross-Sell & Expansion\nA website makes it real\nWe are an ICANN accredited registrar.\nProducts\nWebsite Templates\nWebsites\nDomains\nAI Website Builder\nDesign Intelligence\nOnline Stores\nServices\nInvoicing\nScheduling\nContent & Memberships\nDonations\nPayments\nMarketing Tools\nEmail Campaigns\nProfessional Email\nFeature List\nPricing\nSolutions\nCustomer Examples\nSquarespace Collection\nFitness\nBeauty\nPhotography\nRestaurants\nArt & Design\nWedding\nCreators\nEnterprise\nPrograms\nFor Professionals\nAffiliates\nSupport\nHelp Center\nForum\nWebinars\nHire an Expert\nDeveloper Blog\nDeveloper Platform\nSystem Status\nResources\nExtensions\nSquarespace Blog\nFree Tools\nBusiness Name Generator\nLogo Maker\nCompany\nAbout\nCareers\nOur History\nOur Brand\nAccessibility\nNewsroom\nPress & Media\nContact Us\nFollow\nInstagram\nYoutube\nLinkedin\nFacebook\nX\nEnglish\n$ USD\nTerms\nPrivacy\nSecurity Measures\nSitemap\n© 2025 Squarespace, Inc.", "current_url": "https://www.squarespace.com/careers/jobs/7036031", "has_dynamic_content": false}, "application_analysis": {"application_type": "unknown", "has_direct_form": false, "has_file_upload": true, "requires_external_redirect": false, "apply_button_found": false, "required_fields": [], "file_upload_fields": [{"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown-caret", "visible": false, "tag_name": "svg", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown-header", "visible": false, "tag_name": "div", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "text text--footnote text--light global-navigation__account-dropdown-title", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "text text--footnote text--light global-navigation__account-dropdown-title global-navigation__account-email", "visible": false, "tag_name": "span", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "global-navigation__account-dropdown-list", "visible": false, "tag_name": "ul", "type": ""}, {"name": "", "id": "", "accept": "", "multiple": false, "class": "cta cta--inline cta--light global-navigation__account-dropdown-list-item", "visible": false, "tag_name": "a", "type": ""}], "application_steps": ["1. Manual review required - no clear application path found"], "recommendations": ["Page has file upload capability - can upload resume/CV", "⚠️ No clear application process detected - manual review needed"]}, "timestamp": **********.392904}]