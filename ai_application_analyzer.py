#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json
from typing import Dict, List, Optional

class JobApplicationAnalyzer:
    """AI-powered job application analyzer that reads pages and determines application process"""
    
    def __init__(self):
        self.driver = None
        self.setup_driver()
    
    def setup_driver(self):
        """Set up Chrome driver with stealth options"""
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        service = Service("/usr/local/bin/chromedriver")
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    def analyze_job_page(self, job_url: str) -> Dict:
        """
        Analyze a job page and determine the application process
        """
        try:
            print(f"🔍 Analyzing job page: {job_url}")
            self.driver.get(job_url)
            time.sleep(3)

            # Handle common page obstacles
            self.handle_page_obstacles()

            # Scroll to load dynamic content and find forms
            self.scroll_and_wait_for_content()

            # Extract page information
            page_info = self.extract_page_elements()

            # Analyze the application process using rule-based logic
            application_analysis = self.analyze_application_process(page_info)

            return {
                'url': job_url,
                'page_info': page_info,
                'application_analysis': application_analysis,
                'timestamp': time.time()
            }

        except Exception as e:
            print(f"❌ Error analyzing job page: {e}")
            return {'url': job_url, 'error': str(e)}

    def handle_page_obstacles(self):
        """
        Handle common page obstacles like cookie banners, allow popups, etc.
        """
        print("🍪 Handling page obstacles (cookies, popups, etc.)...")

        # Common cookie/consent banner selectors
        cookie_selectors = [
            # Generic cookie banners
            "[id*='cookie'] button",
            "[class*='cookie'] button",
            "[data-testid*='cookie'] button",
            "[aria-label*='cookie'] button",

            # Accept/Allow buttons
            "button[class*='accept']",
            "button[id*='accept']",
            "button[data-testid*='accept']",
            "[aria-label*='accept'] button",

            # Common consent management platforms
            "#onetrust-accept-btn-handler",
            ".optanon-allow-all",
            "#truste-consent-button",
            ".trustarc-accept-btn",

            # Generic "Allow" buttons
            "button:contains('Allow')",
            "button:contains('Accept')",
            "button:contains('OK')",
            "button:contains('Got it')",
            "button:contains('Continue')",

            # Close buttons for popups
            "[aria-label*='close']",
            "[class*='close']",
            ".modal-close",
            ".popup-close"
        ]

        for selector in cookie_selectors:
            try:
                # Use JavaScript to find and click elements (more reliable)
                script = f"""
                var elements = document.querySelectorAll('{selector}');
                for (var i = 0; i < elements.length; i++) {{
                    var el = elements[i];
                    if (el.offsetParent !== null) {{ // Check if visible
                        el.click();
                        return true;
                    }}
                }}
                return false;
                """

                clicked = self.driver.execute_script(script)
                if clicked:
                    print(f"   ✅ Clicked obstacle element: {selector}")
                    time.sleep(1)
                    break

            except Exception as e:
                continue

        # Handle specific text-based buttons
        text_buttons = [
            "Accept All",
            "Accept Cookies",
            "Allow All",
            "Continue",
            "Got it",
            "OK",
            "Close",
            "Dismiss"
        ]

        for button_text in text_buttons:
            try:
                script = f"""
                var buttons = document.querySelectorAll('button, a, div[role="button"]');
                for (var i = 0; i < buttons.length; i++) {{
                    var btn = buttons[i];
                    if (btn.textContent.trim().toLowerCase().includes('{button_text.lower()}') &&
                        btn.offsetParent !== null) {{
                        btn.click();
                        return true;
                    }}
                }}
                return false;
                """

                clicked = self.driver.execute_script(script)
                if clicked:
                    print(f"   ✅ Clicked text button: {button_text}")
                    time.sleep(1)
                    break

            except Exception as e:
                continue

    def scroll_and_wait_for_content(self):
        """
        Scroll through the page to load dynamic content and wait for forms to appear
        """
        print("📜 Scrolling to load dynamic content and find forms...")

        try:
            # Get initial page height
            last_height = self.driver.execute_script("return document.body.scrollHeight")

            # Scroll in increments to trigger lazy loading
            scroll_positions = [0.25, 0.5, 0.75, 1.0]

            for position in scroll_positions:
                # Scroll to position
                self.driver.execute_script(f"window.scrollTo(0, document.body.scrollHeight * {position});")
                time.sleep(2)

                # Check for new forms or application elements
                forms = self.driver.find_elements(By.TAG_NAME, "form")
                apply_buttons = self.driver.find_elements(By.CSS_SELECTOR, "[class*='apply'], [id*='apply'], button:contains('Apply')")
                file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file']")

                if forms or apply_buttons or file_inputs:
                    print(f"   ✅ Found content at scroll position {position*100}%")
                    print(f"      Forms: {len(forms)}, Apply buttons: {len(apply_buttons)}, File inputs: {len(file_inputs)}")

            # Scroll back to top
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            # Wait for any lazy-loaded content
            print("⏳ Waiting for dynamic content to load...")
            time.sleep(3)

            # Check if page height changed (indicating dynamic content loaded)
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            if new_height != last_height:
                print(f"   ✅ Page height changed: {last_height} -> {new_height} (dynamic content loaded)")

        except Exception as e:
            print(f"   ⚠️ Error during scrolling: {e}")

    def wait_for_application_elements(self):
        """
        Wait for application-specific elements to appear
        """
        print("⏳ Waiting for application elements to load...")

        # Wait for common application elements
        wait = WebDriverWait(self.driver, 10)

        application_selectors = [
            "form",
            "input[type='file']",
            "[class*='apply']",
            "[id*='apply']",
            "[data-qa*='apply']",
            "button:contains('Apply')",
            ".application-form",
            ".job-application"
        ]

        for selector in application_selectors:
            try:
                element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                if element:
                    print(f"   ✅ Found application element: {selector}")
                    return True
            except:
                continue

        print("   ⚠️ No application elements found within timeout")
        return False
    
    def extract_page_elements(self) -> Dict:
        """
        Extract all relevant elements from the job page
        """
        print("📋 Extracting page elements...")

        page_info = {
            'title': '',
            'company': '',
            'location': '',
            'description': '',
            'forms': [],
            'buttons': [],
            'file_inputs': [],
            'text_inputs': [],
            'links': [],
            'page_text': '',
            'current_url': '',
            'has_dynamic_content': False
        }
        
        try:
            # Extract basic info
            page_info['title'] = self.driver.title
            page_info['current_url'] = self.driver.current_url
            page_info['page_text'] = self.driver.find_element(By.TAG_NAME, "body").text[:2000]  # First 2000 chars
            
            # Find all forms
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            for i, form in enumerate(forms):
                form_info = {
                    'index': i,
                    'action': form.get_attribute('action') or '',
                    'method': form.get_attribute('method') or 'GET',
                    'class': form.get_attribute('class') or '',
                    'inputs': [],
                    'textareas': []
                }
                
                # Find inputs within this form
                inputs = form.find_elements(By.TAG_NAME, "input")
                for inp in inputs:
                    input_info = {
                        'type': inp.get_attribute('type') or 'text',
                        'name': inp.get_attribute('name') or '',
                        'id': inp.get_attribute('id') or '',
                        'placeholder': inp.get_attribute('placeholder') or '',
                        'required': inp.get_attribute('required') is not None,
                        'class': inp.get_attribute('class') or ''
                    }
                    form_info['inputs'].append(input_info)
                
                # Find textareas within this form
                textareas = form.find_elements(By.TAG_NAME, "textarea")
                for textarea in textareas:
                    textarea_info = {
                        'name': textarea.get_attribute('name') or '',
                        'id': textarea.get_attribute('id') or '',
                        'placeholder': textarea.get_attribute('placeholder') or '',
                        'required': textarea.get_attribute('required') is not None,
                        'class': textarea.get_attribute('class') or ''
                    }
                    form_info['textareas'].append(textarea_info)
                
                page_info['forms'].append(form_info)
            
            # Find all buttons (including apply buttons) - more comprehensive search
            button_selectors = [
                "button",
                "input[type='submit']",
                "input[type='button']",
                "a[class*='btn']",
                "a[class*='button']",
                "a[class*='apply']",
                "[data-qa*='apply']",
                "[id*='apply']",
                "[class*='apply']",
                "div[role='button']",
                "span[role='button']",
                "[onclick]",
                ".cta",
                ".call-to-action"
            ]

            all_buttons = []
            for selector in button_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    all_buttons.extend(buttons)
                except:
                    continue

            # Also search by text content using JavaScript
            try:
                js_buttons = self.driver.execute_script("""
                    var allElements = document.querySelectorAll('*');
                    var buttonElements = [];
                    var buttonTexts = ['apply', 'submit', 'continue', 'next', 'start application'];

                    for (var i = 0; i < allElements.length; i++) {
                        var el = allElements[i];
                        var text = el.textContent.toLowerCase().trim();

                        for (var j = 0; j < buttonTexts.length; j++) {
                            if (text.includes(buttonTexts[j]) &&
                                (el.tagName === 'BUTTON' || el.tagName === 'A' ||
                                 el.getAttribute('role') === 'button' ||
                                 el.onclick || el.style.cursor === 'pointer')) {
                                buttonElements.push(el);
                                break;
                            }
                        }
                    }
                    return buttonElements.length;
                """)

                if js_buttons > 0:
                    print(f"   ✅ Found {js_buttons} additional buttons via JavaScript")
                    page_info['has_dynamic_content'] = True

            except Exception as e:
                print(f"   ⚠️ JavaScript button search failed: {e}")

            # Process all found buttons
            seen_buttons = set()  # Avoid duplicates
            for button in all_buttons:
                try:
                    button_text = button.text.strip() or button.get_attribute('value') or button.get_attribute('aria-label') or ''
                    button_id = button.get_attribute('id') or ''
                    button_class = button.get_attribute('class') or ''

                    # Create unique identifier to avoid duplicates
                    button_signature = f"{button_text}_{button_id}_{button_class}"

                    if button_text and button_signature not in seen_buttons:
                        seen_buttons.add(button_signature)

                        # Check if button is visible
                        is_visible = button.is_displayed() and button.is_enabled()

                        page_info['buttons'].append({
                            'text': button_text,
                            'type': button.get_attribute('type') or '',
                            'href': button.get_attribute('href') or '',
                            'class': button_class,
                            'id': button_id,
                            'data_qa': button.get_attribute('data-qa') or '',
                            'visible': is_visible,
                            'tag_name': button.tag_name
                        })
                except Exception as e:
                    continue
            
            # Find file upload inputs - comprehensive search
            file_input_selectors = [
                "input[type='file']",
                "[accept*='pdf']",
                "[accept*='doc']",
                "[accept*='resume']",
                "[class*='upload']",
                "[class*='file']",
                "[id*='resume']",
                "[id*='cv']",
                "[id*='upload']",
                "[name*='resume']",
                "[name*='cv']",
                "[name*='upload']"
            ]

            all_file_inputs = []
            for selector in file_input_selectors:
                try:
                    inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    all_file_inputs.extend(inputs)
                except:
                    continue

            # Also look for drag-and-drop upload areas
            try:
                upload_areas = self.driver.find_elements(By.CSS_SELECTOR,
                    "[class*='dropzone'], [class*='drag'], [class*='drop'], .upload-area")
                all_file_inputs.extend(upload_areas)
            except:
                pass

            seen_file_inputs = set()
            for file_input in all_file_inputs:
                try:
                    input_id = file_input.get_attribute('id') or ''
                    input_name = file_input.get_attribute('name') or ''
                    input_class = file_input.get_attribute('class') or ''

                    input_signature = f"{input_id}_{input_name}_{input_class}"

                    if input_signature not in seen_file_inputs:
                        seen_file_inputs.add(input_signature)

                        is_visible = file_input.is_displayed()

                        page_info['file_inputs'].append({
                            'name': input_name,
                            'id': input_id,
                            'accept': file_input.get_attribute('accept') or '',
                            'multiple': file_input.get_attribute('multiple') is not None,
                            'class': input_class,
                            'visible': is_visible,
                            'tag_name': file_input.tag_name,
                            'type': file_input.get_attribute('type') or ''
                        })
                except:
                    continue

            print(f"   📎 Found {len(page_info['file_inputs'])} file upload elements")
            
            # Find text inputs and textareas (not in forms)
            text_input_selectors = [
                "input[type='text']",
                "input[type='email']", 
                "input[type='tel']",
                "input[type='url']",
                "textarea"
            ]
            
            for selector in text_input_selectors:
                inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for inp in inputs:
                    page_info['text_inputs'].append({
                        'type': inp.get_attribute('type') or 'text',
                        'name': inp.get_attribute('name') or '',
                        'id': inp.get_attribute('id') or '',
                        'placeholder': inp.get_attribute('placeholder') or '',
                        'required': inp.get_attribute('required') is not None,
                        'class': inp.get_attribute('class') or ''
                    })
            
            # Find relevant links
            links = self.driver.find_elements(By.TAG_NAME, "a")
            for link in links:
                href = link.get_attribute('href') or ''
                text = link.text.strip()
                if any(keyword in text.lower() for keyword in ['apply', 'application', 'submit', 'career', 'job']):
                    page_info['links'].append({
                        'text': text,
                        'href': href,
                        'class': link.get_attribute('class') or ''
                    })
            
        except Exception as e:
            print(f"⚠️ Error extracting page elements: {e}")

        # Print summary of what we found
        print(f"📊 Extraction Summary:")
        print(f"   📝 Forms: {len(page_info['forms'])}")
        print(f"   🔘 Buttons: {len(page_info['buttons'])}")
        print(f"   📎 File Inputs: {len(page_info['file_inputs'])}")
        print(f"   ✏️ Text Inputs: {len(page_info['text_inputs'])}")
        print(f"   🔗 Relevant Links: {len(page_info['links'])}")

        return page_info
    
    def analyze_application_process(self, page_info: Dict) -> Dict:
        """
        Analyze the application process using rule-based logic
        """
        analysis = {
            'application_type': 'unknown',
            'has_direct_form': False,
            'has_file_upload': False,
            'requires_external_redirect': False,
            'apply_button_found': False,
            'required_fields': [],
            'file_upload_fields': [],
            'application_steps': [],
            'recommendations': []
        }
        
        try:
            # Check for file uploads (resume/CV upload)
            if page_info['file_inputs']:
                analysis['has_file_upload'] = True
                analysis['file_upload_fields'] = page_info['file_inputs']
                analysis['recommendations'].append("Page has file upload capability - can upload resume/CV")
            
            # Check for application forms
            if page_info['forms']:
                analysis['has_direct_form'] = True
                analysis['application_type'] = 'direct_form'
                
                # Analyze form fields
                for form in page_info['forms']:
                    for inp in form['inputs']:
                        if inp['required']:
                            analysis['required_fields'].append({
                                'type': inp['type'],
                                'name': inp['name'],
                                'placeholder': inp['placeholder']
                            })
                
                analysis['recommendations'].append("Page has application form - can fill out directly")
            
            # Check for apply buttons
            apply_buttons = []
            for button in page_info['buttons']:
                button_text = button['text'].lower()
                if any(keyword in button_text for keyword in ['apply', 'submit application', 'apply now']):
                    apply_buttons.append(button)
                    analysis['apply_button_found'] = True
            
            # Check for external redirects
            for link in page_info['links']:
                if link['href'] and 'apply' in link['text'].lower():
                    if link['href'] != page_info['current_url']:
                        analysis['requires_external_redirect'] = True
                        analysis['application_type'] = 'external_redirect'
                        analysis['recommendations'].append(f"Application requires redirect to: {link['href']}")
            
            # Determine application steps
            if analysis['has_direct_form']:
                analysis['application_steps'].append("1. Fill out application form on current page")
                if analysis['has_file_upload']:
                    analysis['application_steps'].append("2. Upload resume/CV file")
                analysis['application_steps'].append("3. Submit application")
            elif analysis['requires_external_redirect']:
                analysis['application_steps'].append("1. Click apply button to redirect to application page")
                analysis['application_steps'].append("2. Complete application on external site")
            elif analysis['apply_button_found']:
                analysis['application_steps'].append("1. Click apply button")
                analysis['application_steps'].append("2. Follow application flow")
            else:
                analysis['application_steps'].append("1. Manual review required - no clear application path found")
                analysis['recommendations'].append("⚠️ No clear application process detected - manual review needed")
            
            # Greenhouse-specific analysis
            if 'greenhouse.io' in page_info['current_url']:
                analysis['platform'] = 'greenhouse'
                analysis['recommendations'].append("✅ Greenhouse platform detected - standard application flow expected")
                
                # Look for Greenhouse-specific elements
                greenhouse_indicators = [
                    'Apply for this job',
                    'Submit Application',
                    'greenhouse',
                    'application-form'
                ]
                
                page_text_lower = page_info['page_text'].lower()
                for indicator in greenhouse_indicators:
                    if indicator in page_text_lower:
                        analysis['recommendations'].append(f"Found Greenhouse indicator: '{indicator}'")
            
        except Exception as e:
            print(f"⚠️ Error analyzing application process: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def close(self):
        """Close the browser driver"""
        if self.driver:
            self.driver.quit()

def analyze_job_urls(job_urls: List[str]) -> List[Dict]:
    """
    Analyze multiple job URLs and return application analysis for each
    """
    analyzer = JobApplicationAnalyzer()
    results = []
    
    try:
        for i, url in enumerate(job_urls, 1):
            print(f"\n🔍 ANALYZING JOB {i}/{len(job_urls)}")
            print("=" * 50)
            
            result = analyzer.analyze_job_page(url)
            results.append(result)
            
            # Show analysis summary
            if 'application_analysis' in result:
                analysis = result['application_analysis']
                print(f"📋 Application Type: {analysis.get('application_type', 'unknown')}")
                print(f"📝 Has Form: {analysis.get('has_direct_form', False)}")
                print(f"📎 Has File Upload: {analysis.get('has_file_upload', False)}")
                print(f"🔗 External Redirect: {analysis.get('requires_external_redirect', False)}")
                print(f"🎯 Apply Button Found: {analysis.get('apply_button_found', False)}")
                
                if analysis.get('recommendations'):
                    print("💡 Recommendations:")
                    for rec in analysis['recommendations']:
                        print(f"   • {rec}")
                
                if analysis.get('application_steps'):
                    print("📋 Application Steps:")
                    for step in analysis['application_steps']:
                        print(f"   {step}")
            
            # Small delay between analyses
            time.sleep(2)
    
    finally:
        analyzer.close()
    
    return results

def save_analysis_results(results: List[Dict], filename: str = "job_application_analysis.json"):
    """Save analysis results to file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Also save a readable summary
        summary_filename = filename.replace('.json', '_summary.txt')
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write("JOB APPLICATION ANALYSIS SUMMARY\n")
            f.write("=" * 50 + "\n\n")
            
            for i, result in enumerate(results, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"URL: {result.get('url', 'Unknown')}\n")
                
                if 'application_analysis' in result:
                    analysis = result['application_analysis']
                    f.write(f"Application Type: {analysis.get('application_type', 'unknown')}\n")
                    f.write(f"Has Form: {analysis.get('has_direct_form', False)}\n")
                    f.write(f"Has File Upload: {analysis.get('has_file_upload', False)}\n")
                    f.write(f"External Redirect: {analysis.get('requires_external_redirect', False)}\n")
                    f.write(f"Apply Button Found: {analysis.get('apply_button_found', False)}\n")
                    
                    if analysis.get('required_fields'):
                        f.write("Required Fields:\n")
                        for field in analysis['required_fields']:
                            f.write(f"  - {field['type']}: {field['name']} ({field['placeholder']})\n")
                    
                    if analysis.get('recommendations'):
                        f.write("Recommendations:\n")
                        for rec in analysis['recommendations']:
                            f.write(f"  • {rec}\n")
                    
                    if analysis.get('application_steps'):
                        f.write("Application Steps:\n")
                        for step in analysis['application_steps']:
                            f.write(f"  {step}\n")
                
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"💾 Analysis results saved to {filename} and {summary_filename}")
        
    except Exception as e:
        print(f"❌ Error saving analysis results: {e}")

if __name__ == "__main__":
    # Test with the 5 job URLs we extracted
    job_urls = [
        "https://boards.greenhouse.io/waymo/jobs/6508003",
        "https://boards.greenhouse.io/coveoen/jobs/7944459002", 
        "https://boards.greenhouse.io/waymo/jobs/6885682",
        "http://job-boards.greenhouse.io/nerdy/jobs/7045082",
        "https://boards.greenhouse.io/squarespace/jobs/7036031"
    ]
    
    print("🤖 AI-POWERED JOB APPLICATION ANALYZER")
    print("=" * 60)
    print(f"📋 Analyzing {len(job_urls)} job pages...")
    
    results = analyze_job_urls(job_urls)
    save_analysis_results(results)
    
    print(f"\n🎉 Analysis complete! Analyzed {len(results)} job pages.")
    print("💡 Check the generated files for detailed analysis results.")
