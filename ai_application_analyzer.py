#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json
from typing import Dict, List, Optional

class JobApplicationAnalyzer:
    """AI-powered job application analyzer that reads pages and determines application process"""
    
    def __init__(self):
        self.driver = None
        self.setup_driver()
    
    def setup_driver(self):
        """Set up Chrome driver with stealth options"""
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        service = Service("/usr/local/bin/chromedriver")
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    def analyze_job_page(self, job_url: str) -> Dict:
        """
        Analyze a job page and determine the application process
        """
        try:
            print(f"🔍 Analyzing job page: {job_url}")
            self.driver.get(job_url)
            time.sleep(3)
            
            # Extract page information
            page_info = self.extract_page_elements()
            
            # Analyze the application process using rule-based logic
            application_analysis = self.analyze_application_process(page_info)
            
            return {
                'url': job_url,
                'page_info': page_info,
                'application_analysis': application_analysis,
                'timestamp': time.time()
            }
            
        except Exception as e:
            print(f"❌ Error analyzing job page: {e}")
            return {'url': job_url, 'error': str(e)}
    
    def extract_page_elements(self) -> Dict:
        """
        Extract all relevant elements from the job page
        """
        page_info = {
            'title': '',
            'company': '',
            'location': '',
            'description': '',
            'forms': [],
            'buttons': [],
            'file_inputs': [],
            'text_inputs': [],
            'links': [],
            'page_text': '',
            'current_url': ''
        }
        
        try:
            # Extract basic info
            page_info['title'] = self.driver.title
            page_info['current_url'] = self.driver.current_url
            page_info['page_text'] = self.driver.find_element(By.TAG_NAME, "body").text[:2000]  # First 2000 chars
            
            # Find all forms
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            for i, form in enumerate(forms):
                form_info = {
                    'index': i,
                    'action': form.get_attribute('action') or '',
                    'method': form.get_attribute('method') or 'GET',
                    'class': form.get_attribute('class') or '',
                    'inputs': [],
                    'textareas': []
                }
                
                # Find inputs within this form
                inputs = form.find_elements(By.TAG_NAME, "input")
                for inp in inputs:
                    input_info = {
                        'type': inp.get_attribute('type') or 'text',
                        'name': inp.get_attribute('name') or '',
                        'id': inp.get_attribute('id') or '',
                        'placeholder': inp.get_attribute('placeholder') or '',
                        'required': inp.get_attribute('required') is not None,
                        'class': inp.get_attribute('class') or ''
                    }
                    form_info['inputs'].append(input_info)
                
                # Find textareas within this form
                textareas = form.find_elements(By.TAG_NAME, "textarea")
                for textarea in textareas:
                    textarea_info = {
                        'name': textarea.get_attribute('name') or '',
                        'id': textarea.get_attribute('id') or '',
                        'placeholder': textarea.get_attribute('placeholder') or '',
                        'required': textarea.get_attribute('required') is not None,
                        'class': textarea.get_attribute('class') or ''
                    }
                    form_info['textareas'].append(textarea_info)
                
                page_info['forms'].append(form_info)
            
            # Find all buttons (including apply buttons)
            button_selectors = [
                "button",
                "input[type='submit']",
                "input[type='button']",
                "a[class*='btn']",
                "a[class*='button']",
                "a[class*='apply']",
                "[data-qa*='apply']",
                "[id*='apply']"
            ]
            
            all_buttons = []
            for selector in button_selectors:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                all_buttons.extend(buttons)
            
            for button in all_buttons:
                button_text = button.text.strip() or button.get_attribute('value') or button.get_attribute('aria-label') or ''
                if button_text:
                    page_info['buttons'].append({
                        'text': button_text,
                        'type': button.get_attribute('type') or '',
                        'href': button.get_attribute('href') or '',
                        'class': button.get_attribute('class') or '',
                        'id': button.get_attribute('id') or '',
                        'data_qa': button.get_attribute('data-qa') or ''
                    })
            
            # Find file upload inputs
            file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
            for file_input in file_inputs:
                page_info['file_inputs'].append({
                    'name': file_input.get_attribute('name') or '',
                    'id': file_input.get_attribute('id') or '',
                    'accept': file_input.get_attribute('accept') or '',
                    'multiple': file_input.get_attribute('multiple') is not None,
                    'class': file_input.get_attribute('class') or ''
                })
            
            # Find text inputs and textareas (not in forms)
            text_input_selectors = [
                "input[type='text']",
                "input[type='email']", 
                "input[type='tel']",
                "input[type='url']",
                "textarea"
            ]
            
            for selector in text_input_selectors:
                inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for inp in inputs:
                    page_info['text_inputs'].append({
                        'type': inp.get_attribute('type') or 'text',
                        'name': inp.get_attribute('name') or '',
                        'id': inp.get_attribute('id') or '',
                        'placeholder': inp.get_attribute('placeholder') or '',
                        'required': inp.get_attribute('required') is not None,
                        'class': inp.get_attribute('class') or ''
                    })
            
            # Find relevant links
            links = self.driver.find_elements(By.TAG_NAME, "a")
            for link in links:
                href = link.get_attribute('href') or ''
                text = link.text.strip()
                if any(keyword in text.lower() for keyword in ['apply', 'application', 'submit', 'career', 'job']):
                    page_info['links'].append({
                        'text': text,
                        'href': href,
                        'class': link.get_attribute('class') or ''
                    })
            
        except Exception as e:
            print(f"⚠️ Error extracting page elements: {e}")
        
        return page_info
    
    def analyze_application_process(self, page_info: Dict) -> Dict:
        """
        Analyze the application process using rule-based logic
        """
        analysis = {
            'application_type': 'unknown',
            'has_direct_form': False,
            'has_file_upload': False,
            'requires_external_redirect': False,
            'apply_button_found': False,
            'required_fields': [],
            'file_upload_fields': [],
            'application_steps': [],
            'recommendations': []
        }
        
        try:
            # Check for file uploads (resume/CV upload)
            if page_info['file_inputs']:
                analysis['has_file_upload'] = True
                analysis['file_upload_fields'] = page_info['file_inputs']
                analysis['recommendations'].append("Page has file upload capability - can upload resume/CV")
            
            # Check for application forms
            if page_info['forms']:
                analysis['has_direct_form'] = True
                analysis['application_type'] = 'direct_form'
                
                # Analyze form fields
                for form in page_info['forms']:
                    for inp in form['inputs']:
                        if inp['required']:
                            analysis['required_fields'].append({
                                'type': inp['type'],
                                'name': inp['name'],
                                'placeholder': inp['placeholder']
                            })
                
                analysis['recommendations'].append("Page has application form - can fill out directly")
            
            # Check for apply buttons
            apply_buttons = []
            for button in page_info['buttons']:
                button_text = button['text'].lower()
                if any(keyword in button_text for keyword in ['apply', 'submit application', 'apply now']):
                    apply_buttons.append(button)
                    analysis['apply_button_found'] = True
            
            # Check for external redirects
            for link in page_info['links']:
                if link['href'] and 'apply' in link['text'].lower():
                    if link['href'] != page_info['current_url']:
                        analysis['requires_external_redirect'] = True
                        analysis['application_type'] = 'external_redirect'
                        analysis['recommendations'].append(f"Application requires redirect to: {link['href']}")
            
            # Determine application steps
            if analysis['has_direct_form']:
                analysis['application_steps'].append("1. Fill out application form on current page")
                if analysis['has_file_upload']:
                    analysis['application_steps'].append("2. Upload resume/CV file")
                analysis['application_steps'].append("3. Submit application")
            elif analysis['requires_external_redirect']:
                analysis['application_steps'].append("1. Click apply button to redirect to application page")
                analysis['application_steps'].append("2. Complete application on external site")
            elif analysis['apply_button_found']:
                analysis['application_steps'].append("1. Click apply button")
                analysis['application_steps'].append("2. Follow application flow")
            else:
                analysis['application_steps'].append("1. Manual review required - no clear application path found")
                analysis['recommendations'].append("⚠️ No clear application process detected - manual review needed")
            
            # Greenhouse-specific analysis
            if 'greenhouse.io' in page_info['current_url']:
                analysis['platform'] = 'greenhouse'
                analysis['recommendations'].append("✅ Greenhouse platform detected - standard application flow expected")
                
                # Look for Greenhouse-specific elements
                greenhouse_indicators = [
                    'Apply for this job',
                    'Submit Application',
                    'greenhouse',
                    'application-form'
                ]
                
                page_text_lower = page_info['page_text'].lower()
                for indicator in greenhouse_indicators:
                    if indicator in page_text_lower:
                        analysis['recommendations'].append(f"Found Greenhouse indicator: '{indicator}'")
            
        except Exception as e:
            print(f"⚠️ Error analyzing application process: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def close(self):
        """Close the browser driver"""
        if self.driver:
            self.driver.quit()

def analyze_job_urls(job_urls: List[str]) -> List[Dict]:
    """
    Analyze multiple job URLs and return application analysis for each
    """
    analyzer = JobApplicationAnalyzer()
    results = []
    
    try:
        for i, url in enumerate(job_urls, 1):
            print(f"\n🔍 ANALYZING JOB {i}/{len(job_urls)}")
            print("=" * 50)
            
            result = analyzer.analyze_job_page(url)
            results.append(result)
            
            # Show analysis summary
            if 'application_analysis' in result:
                analysis = result['application_analysis']
                print(f"📋 Application Type: {analysis.get('application_type', 'unknown')}")
                print(f"📝 Has Form: {analysis.get('has_direct_form', False)}")
                print(f"📎 Has File Upload: {analysis.get('has_file_upload', False)}")
                print(f"🔗 External Redirect: {analysis.get('requires_external_redirect', False)}")
                print(f"🎯 Apply Button Found: {analysis.get('apply_button_found', False)}")
                
                if analysis.get('recommendations'):
                    print("💡 Recommendations:")
                    for rec in analysis['recommendations']:
                        print(f"   • {rec}")
                
                if analysis.get('application_steps'):
                    print("📋 Application Steps:")
                    for step in analysis['application_steps']:
                        print(f"   {step}")
            
            # Small delay between analyses
            time.sleep(2)
    
    finally:
        analyzer.close()
    
    return results

def save_analysis_results(results: List[Dict], filename: str = "job_application_analysis.json"):
    """Save analysis results to file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Also save a readable summary
        summary_filename = filename.replace('.json', '_summary.txt')
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write("JOB APPLICATION ANALYSIS SUMMARY\n")
            f.write("=" * 50 + "\n\n")
            
            for i, result in enumerate(results, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"URL: {result.get('url', 'Unknown')}\n")
                
                if 'application_analysis' in result:
                    analysis = result['application_analysis']
                    f.write(f"Application Type: {analysis.get('application_type', 'unknown')}\n")
                    f.write(f"Has Form: {analysis.get('has_direct_form', False)}\n")
                    f.write(f"Has File Upload: {analysis.get('has_file_upload', False)}\n")
                    f.write(f"External Redirect: {analysis.get('requires_external_redirect', False)}\n")
                    f.write(f"Apply Button Found: {analysis.get('apply_button_found', False)}\n")
                    
                    if analysis.get('required_fields'):
                        f.write("Required Fields:\n")
                        for field in analysis['required_fields']:
                            f.write(f"  - {field['type']}: {field['name']} ({field['placeholder']})\n")
                    
                    if analysis.get('recommendations'):
                        f.write("Recommendations:\n")
                        for rec in analysis['recommendations']:
                            f.write(f"  • {rec}\n")
                    
                    if analysis.get('application_steps'):
                        f.write("Application Steps:\n")
                        for step in analysis['application_steps']:
                            f.write(f"  {step}\n")
                
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"💾 Analysis results saved to {filename} and {summary_filename}")
        
    except Exception as e:
        print(f"❌ Error saving analysis results: {e}")

if __name__ == "__main__":
    # Test with the 5 job URLs we extracted
    job_urls = [
        "https://boards.greenhouse.io/waymo/jobs/6508003",
        "https://boards.greenhouse.io/coveoen/jobs/7944459002", 
        "https://boards.greenhouse.io/waymo/jobs/6885682",
        "http://job-boards.greenhouse.io/nerdy/jobs/7045082",
        "https://boards.greenhouse.io/squarespace/jobs/7036031"
    ]
    
    print("🤖 AI-POWERED JOB APPLICATION ANALYZER")
    print("=" * 60)
    print(f"📋 Analyzing {len(job_urls)} job pages...")
    
    results = analyze_job_urls(job_urls)
    save_analysis_results(results)
    
    print(f"\n🎉 Analysis complete! Analyzed {len(results)} job pages.")
    print("💡 Check the generated files for detailed analysis results.")
