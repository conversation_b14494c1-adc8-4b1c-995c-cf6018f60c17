#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import random
import json

def setup_stealth_driver():
    """Set up Chrome driver with stealth options"""
    options = webdriver.ChromeOptions()
    
    # Stealth options
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    
    # Hide webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def human_like_delay():
    """Add random human-like delays"""
    time.sleep(random.uniform(2, 4))

def extract_job_details_from_link(driver, job_url, job_title):
    """
    Visit a job link and extract detailed information
    """
    job_details = {
        'title': job_title,
        'link': job_url,
        'company': '',
        'location': '',
        'salary': '',
        'description': '',
        'requirements': '',
        'benefits': '',
        'application_link': '',
        'posted_date': '',
        'job_type': '',
        'source': 'Detailed Scraping'
    }
    
    try:
        print(f"   🔍 Visiting: {job_url}")
        
        # Open job link in new tab
        driver.execute_script("window.open('');")
        driver.switch_to.window(driver.window_handles[-1])
        driver.get(job_url)
        human_like_delay()
        
        # Extract detailed information based on the site
        if "greenhouse.io" in job_url:
            job_details = extract_greenhouse_details(driver, job_details)
        elif "lever.co" in job_url:
            job_details = extract_lever_details(driver, job_details)
        elif "workday.com" in job_url:
            job_details = extract_workday_details(driver, job_details)
        else:
            job_details = extract_generic_details(driver, job_details)
        
        # Close the tab and return to main window
        driver.close()
        driver.switch_to.window(driver.window_handles[0])
        
        return job_details
        
    except Exception as e:
        print(f"   ❌ Error extracting details: {e}")
        # Make sure we close any extra tabs
        if len(driver.window_handles) > 1:
            driver.close()
            driver.switch_to.window(driver.window_handles[0])
        return job_details

def extract_greenhouse_details(driver, job_details):
    """Extract details from Greenhouse job pages"""
    try:
        # Company name
        try:
            company_element = driver.find_element(By.CSS_SELECTOR, ".company-name, .header-company-name")
            job_details['company'] = company_element.text.strip()
        except:
            pass
        
        # Location
        try:
            location_element = driver.find_element(By.CSS_SELECTOR, ".location, .job-location")
            job_details['location'] = location_element.text.strip()
        except:
            pass
        
        # Job description
        try:
            desc_element = driver.find_element(By.CSS_SELECTOR, ".job-post-content, .content")
            job_details['description'] = desc_element.text.strip()
        except:
            pass
        
        # Application link
        try:
            apply_button = driver.find_element(By.CSS_SELECTOR, ".btn-apply, .application-button")
            job_details['application_link'] = apply_button.get_attribute("href") or driver.current_url
        except:
            job_details['application_link'] = driver.current_url
        
    except Exception as e:
        print(f"   ⚠️ Error extracting Greenhouse details: {e}")
    
    return job_details

def extract_lever_details(driver, job_details):
    """Extract details from Lever job pages"""
    try:
        # Company name
        try:
            company_element = driver.find_element(By.CSS_SELECTOR, ".company-name")
            job_details['company'] = company_element.text.strip()
        except:
            pass
        
        # Location
        try:
            location_element = driver.find_element(By.CSS_SELECTOR, ".location")
            job_details['location'] = location_element.text.strip()
        except:
            pass
        
        # Job description
        try:
            desc_element = driver.find_element(By.CSS_SELECTOR, ".section-wrapper")
            job_details['description'] = desc_element.text.strip()
        except:
            pass
        
        job_details['application_link'] = driver.current_url
        
    except Exception as e:
        print(f"   ⚠️ Error extracting Lever details: {e}")
    
    return job_details

def extract_workday_details(driver, job_details):
    """Extract details from Workday job pages"""
    try:
        # Job description
        try:
            desc_element = driver.find_element(By.CSS_SELECTOR, "[data-automation-id='jobPostingDescription']")
            job_details['description'] = desc_element.text.strip()
        except:
            pass
        
        job_details['application_link'] = driver.current_url
        
    except Exception as e:
        print(f"   ⚠️ Error extracting Workday details: {e}")
    
    return job_details

def extract_generic_details(driver, job_details):
    """Extract details from generic job pages"""
    try:
        # Try to find job description in common containers
        desc_selectors = [
            ".job-description",
            ".description",
            ".content",
            ".job-content",
            "[class*='description']",
            "main",
            ".main-content"
        ]
        
        for selector in desc_selectors:
            try:
                desc_element = driver.find_element(By.CSS_SELECTOR, selector)
                description = desc_element.text.strip()
                if len(description) > 100:  # Only use if substantial content
                    job_details['description'] = description
                    break
            except:
                continue
        
        job_details['application_link'] = driver.current_url
        
    except Exception as e:
        print(f"   ⚠️ Error extracting generic details: {e}")
    
    return job_details

def detailed_google_search():
    """
    Search Google and visit each result individually for detailed extraction
    """
    driver = None
    jobs = []
    
    try:
        print("🔍 DETAILED JOB SCRAPER - VISITING EACH LINK")
        print("=" * 60)
        
        driver = setup_stealth_driver()
        print("✅ Stealth Chrome driver created!")
        
        # Visit Google homepage first
        print("🌐 Visiting Google homepage...")
        driver.get("https://www.google.com")
        human_like_delay()
        
        # Navigate to search results
        search_url = "https://www.google.com/search?q=%22Full%20Stack%20Engineer%22+site:greenhouse.io+remote&tbs=qdr:d"
        print("🔍 Loading search results...")
        driver.get(search_url)
        human_like_delay()
        
        # Check if blocked
        page_text = driver.find_element(By.TAG_NAME, "body").text.lower()
        if "unusual traffic" in page_text or "captcha" in page_text:
            print("🚫 Got blocked by Google")
            return []
        
        print("✅ Successfully loaded search results!")
        
        # Extract all search result links
        search_results = []
        selectors_to_try = ["div.g", "div[data-ved]", ".g", "div.yuRUbf", "div.tF2Cxc"]
        
        for selector in selectors_to_try:
            try:
                results = driver.find_elements(By.CSS_SELECTOR, selector)
                if results:
                    search_results = results
                    print(f"✅ Found {len(search_results)} search results")
                    break
            except:
                continue
        
        if not search_results:
            print("❌ No search results found")
            return []
        
        # Extract basic info and links from first 5 results
        job_links = []
        print(f"\n📋 Extracting links from first 5 results...")
        
        for i, result in enumerate(search_results[:5]):
            try:
                # Get title
                title = ""
                title_selectors = ["h3", ".LC20lb", ".DKV0Md"]
                for selector in title_selectors:
                    try:
                        title_element = result.find_element(By.CSS_SELECTOR, selector)
                        title = title_element.text.strip()
                        if title:
                            break
                    except:
                        continue
                
                # Get link
                link = ""
                link_selectors = ["a", "h3 a", ".yuRUbf a"]
                for selector in link_selectors:
                    try:
                        link_element = result.find_element(By.CSS_SELECTOR, selector)
                        link = link_element.get_attribute("href")
                        if link and link.startswith("http"):
                            break
                    except:
                        continue
                
                if title and link:
                    job_links.append({'title': title, 'link': link})
                    print(f"   {i+1}. {title}")
                    print(f"      🔗 {link}")
                
            except Exception as e:
                print(f"   ⚠️ Error extracting result {i+1}: {e}")
                continue
        
        print(f"\n🎯 Now visiting each of the {len(job_links)} job links for detailed info...")
        print("=" * 60)
        
        # Visit each job link individually
        for i, job_info in enumerate(job_links, 1):
            print(f"\n📌 JOB {i}/{len(job_links)}: {job_info['title']}")
            
            detailed_job = extract_job_details_from_link(driver, job_info['link'], job_info['title'])
            jobs.append(detailed_job)
            
            print(f"   ✅ Company: {detailed_job['company'] or 'Not found'}")
            print(f"   📍 Location: {detailed_job['location'] or 'Not found'}")
            print(f"   📝 Description: {len(detailed_job['description'])} characters")
            
            # Small delay between requests
            human_like_delay()
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during detailed search: {e}")
        return []
        
    finally:
        if driver:
            try:
                print("\n🕐 Keeping browser open for 10 seconds...")
                time.sleep(10)
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def save_detailed_jobs(jobs, filename="detailed_jobs.json"):
    """Save detailed job information"""
    try:
        # Save as JSON
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(jobs, f, indent=2, ensure_ascii=False)
        
        # Save as detailed text report
        txt_filename = filename.replace('.json', '_report.txt')
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write("DETAILED FULL STACK ENGINEER JOB REPORT\n")
            f.write("=" * 60 + "\n\n")
            
            for i, job in enumerate(jobs, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"Title: {job['title']}\n")
                f.write(f"Company: {job['company'] or 'Not specified'}\n")
                f.write(f"Location: {job['location'] or 'Not specified'}\n")
                f.write(f"Job Type: {job['job_type'] or 'Not specified'}\n")
                f.write(f"Salary: {job['salary'] or 'Not specified'}\n")
                f.write(f"Link: {job['link']}\n")
                f.write(f"Application Link: {job['application_link'] or job['link']}\n")
                f.write(f"Source: {job['source']}\n")
                f.write(f"\nDESCRIPTION:\n{'-' * 20}\n")
                f.write(f"{job['description'][:1000]}{'...' if len(job['description']) > 1000 else ''}\n")
                f.write("\n" + "=" * 60 + "\n\n")
        
        print(f"💾 Detailed jobs saved to: {filename} and {txt_filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving jobs: {e}")
        return False

def main():
    """Main function"""
    print("=" * 70)
    print("🔍 DETAILED JOB SCRAPER - VISIT EACH LINK INDIVIDUALLY")
    print("=" * 70)
    
    jobs = detailed_google_search()
    
    if jobs:
        print(f"\n🎉 SUCCESS! Extracted detailed info from {len(jobs)} jobs!")
        
        # Show summary
        print("\n📋 SUMMARY:")
        print("-" * 40)
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job['title']}")
            print(f"   🏢 Company: {job['company'] or 'Unknown'}")
            print(f"   📍 Location: {job['location'] or 'Unknown'}")
            print(f"   🔗 {job['link']}")
            print()
        
        # Save results
        save_detailed_jobs(jobs)
        
    else:
        print("\n❌ No jobs found or extracted")

if __name__ == "__main__":
    main()
