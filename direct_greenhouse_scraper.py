#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json
import requests

def setup_driver():
    """Set up Chrome driver"""
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    return driver

def scrape_greenhouse_directly():
    """
    Bypass Google entirely - go directly to greenhouse.io job boards
    """
    driver = None
    jobs = []
    
    # List of popular companies that use Greenhouse for Full Stack roles
    greenhouse_companies = [
        "https://boards.greenhouse.io/stripe",
        "https://boards.greenhouse.io/airbnb",
        "https://boards.greenhouse.io/shopify",
        "https://boards.greenhouse.io/notion",
        "https://boards.greenhouse.io/figma",
        "https://boards.greenhouse.io/discord",
        "https://boards.greenhouse.io/coinbase",
        "https://boards.greenhouse.io/uber",
        "https://boards.greenhouse.io/lyft",
        "https://boards.greenhouse.io/dropbox",
        "https://boards.greenhouse.io/pinterest",
        "https://boards.greenhouse.io/reddit",
        "https://boards.greenhouse.io/twitch",
        "https://boards.greenhouse.io/robinhood",
        "https://boards.greenhouse.io/doordash",
    ]
    
    try:
        print("🎯 DIRECT GREENHOUSE SCRAPING (NO GOOGLE)")
        print("=" * 50)
        
        driver = setup_driver()
        print("✅ Chrome driver created!")
        
        for company_url in greenhouse_companies:
            company_name = company_url.split("/")[-1].title()
            print(f"\n🏢 Checking {company_name}...")
            
            try:
                driver.get(company_url)
                time.sleep(3)
                
                # Look for Full Stack Engineer positions
                job_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='jobs']")
                
                for job_link in job_links:
                    job_text = job_link.text.lower()
                    job_title = job_link.text.strip()
                    
                    # Check if it's a Full Stack role
                    if any(keyword in job_text for keyword in ['full stack', 'fullstack', 'full-stack']):
                        # Check if it's remote or mentions remote
                        job_url = job_link.get_attribute("href")
                        
                        # Get more details by visiting the job page
                        try:
                            driver.execute_script("window.open('');")
                            driver.switch_to.window(driver.window_handles[1])
                            driver.get(job_url)
                            time.sleep(2)
                            
                            # Extract job details
                            try:
                                job_description = driver.find_element(By.CSS_SELECTOR, ".job-post-content, .content").text
                                is_remote = any(word in job_description.lower() for word in ['remote', 'work from home', 'distributed', 'anywhere'])
                                
                                if is_remote:
                                    job = {
                                        'title': job_title,
                                        'company': company_name,
                                        'link': job_url,
                                        'description': job_description[:500] + "..." if len(job_description) > 500 else job_description,
                                        'source': 'Direct Greenhouse Scraping',
                                        'remote': True
                                    }
                                    jobs.append(job)
                                    
                                    print(f"   ✅ Found: {job_title}")
                                    print(f"      🔗 {job_url}")
                                
                            except Exception as e:
                                print(f"   ⚠️ Could not extract details: {e}")
                            
                            driver.close()
                            driver.switch_to.window(driver.window_handles[0])
                            
                        except Exception as e:
                            print(f"   ⚠️ Error checking job details: {e}")
                            if len(driver.window_handles) > 1:
                                driver.close()
                                driver.switch_to.window(driver.window_handles[0])
                
            except Exception as e:
                print(f"   ❌ Error checking {company_name}: {e}")
                continue
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during direct scraping: {e}")
        return []
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def try_greenhouse_api_approach():
    """
    Try to find Greenhouse API endpoints or RSS feeds
    """
    print("\n🔍 Trying alternative data sources...")
    
    # Some companies have public RSS feeds or APIs
    potential_sources = [
        "https://boards.greenhouse.io/embed/job_board?for=stripe",
        "https://boards.greenhouse.io/embed/job_board?for=airbnb",
        "https://boards.greenhouse.io/embed/job_board?for=notion",
    ]
    
    jobs = []
    
    for source in potential_sources:
        try:
            print(f"📡 Checking: {source}")
            response = requests.get(source, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ Got response from {source}")
                # This would need parsing based on the actual response format
                # For now, just indicate we got a response
            else:
                print(f"   ❌ No response from {source}")
        except Exception as e:
            print(f"   ⚠️ Error with {source}: {e}")
    
    return jobs

def search_job_aggregators():
    """
    Search job aggregator sites that are less likely to block
    """
    driver = None
    jobs = []
    
    try:
        print("\n🌐 Searching job aggregator sites...")
        
        driver = setup_driver()
        
        # Try Indeed (less likely to block than Google)
        indeed_url = "https://www.indeed.com/jobs?q=%22full+stack+engineer%22+greenhouse.io&l=Remote"
        
        print("🔍 Checking Indeed...")
        driver.get(indeed_url)
        time.sleep(3)
        
        # Extract Indeed results
        job_cards = driver.find_elements(By.CSS_SELECTOR, "[data-jk]")
        
        for card in job_cards[:5]:
            try:
                title_element = card.find_element(By.CSS_SELECTOR, "h2 a span")
                title = title_element.text.strip()
                
                link_element = card.find_element(By.CSS_SELECTOR, "h2 a")
                link = link_element.get_attribute("href")
                
                company_element = card.find_element(By.CSS_SELECTOR, "[data-testid='company-name']")
                company = company_element.text.strip()
                
                if "greenhouse" in card.text.lower():
                    job = {
                        'title': title,
                        'company': company,
                        'link': link,
                        'description': "Found on Indeed - likely uses Greenhouse",
                        'source': 'Indeed Search',
                        'remote': True
                    }
                    jobs.append(job)
                    
                    print(f"   ✅ Found: {title} at {company}")
                
            except Exception as e:
                print(f"   ⚠️ Error extracting Indeed result: {e}")
                continue
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error with job aggregators: {e}")
        return []
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🧹 Browser closed.")
            except:
                pass

def main():
    """Main function with multiple approaches"""
    print("=" * 60)
    print("🎯 DIRECT GREENHOUSE JOB SCRAPER")
    print("🚫 NO GOOGLE - NO CAPTCHA!")
    print("=" * 60)
    
    all_jobs = []
    
    # Method 1: Direct Greenhouse scraping
    print("\n📋 METHOD 1: Direct Greenhouse Company Pages")
    direct_jobs = scrape_greenhouse_directly()
    all_jobs.extend(direct_jobs)
    
    # Method 2: Job aggregators
    print("\n📋 METHOD 2: Job Aggregator Sites")
    aggregator_jobs = search_job_aggregators()
    all_jobs.extend(aggregator_jobs)
    
    # Method 3: API approach
    api_jobs = try_greenhouse_api_approach()
    all_jobs.extend(api_jobs)
    
    # Results
    if all_jobs:
        print(f"\n🎉 SUCCESS! Found {len(all_jobs)} Full Stack Engineer jobs!")
        
        # Remove duplicates
        unique_jobs = []
        seen_links = set()
        for job in all_jobs:
            if job['link'] not in seen_links:
                unique_jobs.append(job)
                seen_links.add(job['link'])
        
        print(f"📋 {len(unique_jobs)} unique jobs after removing duplicates")
        
        # Save results
        try:
            with open("direct_greenhouse_jobs.json", 'w', encoding='utf-8') as f:
                json.dump(unique_jobs, f, indent=2, ensure_ascii=False)
            
            with open("direct_greenhouse_jobs.txt", 'w', encoding='utf-8') as f:
                f.write("FULL STACK ENGINEER JOBS - DIRECT GREENHOUSE SCRAPING\n")
                f.write("=" * 60 + "\n\n")
                
                for i, job in enumerate(unique_jobs, 1):
                    f.write(f"JOB {i}:\n")
                    f.write(f"Title: {job['title']}\n")
                    f.write(f"Company: {job.get('company', 'N/A')}\n")
                    f.write(f"Link: {job['link']}\n")
                    f.write(f"Remote: {job.get('remote', 'Unknown')}\n")
                    f.write(f"Source: {job['source']}\n")
                    f.write(f"Description: {job['description'][:200]}...\n")
                    f.write("-" * 50 + "\n\n")
            
            print("💾 Results saved to direct_greenhouse_jobs.json and .txt")
            
            # Show summary
            print("\n📋 SUMMARY:")
            for i, job in enumerate(unique_jobs, 1):
                print(f"{i}. {job['title']} at {job.get('company', 'Unknown')}")
                print(f"   🔗 {job['link']}")
                print()
        
        except Exception as e:
            print(f"❌ Error saving results: {e}")
    
    else:
        print("\n❌ No jobs found with any method")
        print("💡 This could mean:")
        print("   • No new Full Stack positions available")
        print("   • Companies are not actively hiring")
        print("   • Need to check more company pages")

if __name__ == "__main__":
    main()
