#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time
import json

def setup_driver():
    """Set up Chrome driver"""
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def extract_and_visit_links():
    """
    Extract all links from Google search and visit greenhouse.io ones
    """
    driver = None
    jobs = []
    
    try:
        print("🔍 SIMPLE LINK EXTRACTOR AND VISITOR")
        print("=" * 50)
        
        driver = setup_driver()
        print("✅ Chrome driver created!")
        
        # Go directly to the search URL
        search_url = "https://www.google.com/search?q=%22Full%20Stack%20Engineer%22+site:greenhouse.io+remote&tbs=qdr:d"
        print("🌐 Opening Google search...")
        driver.get(search_url)
        time.sleep(5)
        
        print(f"📄 Page title: {driver.title}")
        
        # Check if blocked
        if "unusual traffic" in driver.page_source.lower():
            print("🚫 Blocked by Google")
            return []
        
        print("✅ Successfully loaded search page!")
        
        # Get ALL links on the page
        print("🔗 Extracting all links...")
        all_links = driver.find_elements(By.CSS_SELECTOR, "a[href]")
        print(f"📊 Found {len(all_links)} total links on page")
        
        # Filter for greenhouse.io links
        greenhouse_links = []
        for link in all_links:
            try:
                href = link.get_attribute("href")
                text = link.text.strip()
                
                if href and "greenhouse.io" in href.lower():
                    # Skip Google's internal links
                    if not any(skip in href for skip in ['google.com', 'accounts.google', 'support.google']):
                        greenhouse_links.append({
                            'text': text,
                            'url': href
                        })
            except:
                continue
        
        print(f"🎯 Found {len(greenhouse_links)} greenhouse.io links!")
        
        if not greenhouse_links:
            print("❌ No greenhouse.io links found. Let's check what links we do have...")
            
            # Show first 10 links for debugging
            print("\n🔍 First 10 links on the page:")
            for i, link in enumerate(all_links[:10]):
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip()[:50]
                    print(f"   {i+1}. '{text}' -> {href}")
                except:
                    continue
            
            return []
        
        # Show the greenhouse links we found
        print(f"\n📋 Greenhouse.io links found:")
        for i, link_info in enumerate(greenhouse_links, 1):
            print(f"   {i}. '{link_info['text']}' -> {link_info['url']}")
        
        # Now visit each greenhouse link
        print(f"\n🎯 Visiting each of the {len(greenhouse_links)} greenhouse.io links...")
        print("=" * 60)
        
        for i, link_info in enumerate(greenhouse_links, 1):
            print(f"\n📌 VISITING LINK {i}/{len(greenhouse_links)}")
            print(f"🔗 URL: {link_info['url']}")
            
            try:
                # Open in new tab
                driver.execute_script("window.open('');")
                driver.switch_to.window(driver.window_handles[-1])
                driver.get(link_info['url'])
                time.sleep(3)
                
                # Extract job details
                job_details = {
                    'title': link_info['text'],
                    'url': link_info['url'],
                    'company': '',
                    'location': '',
                    'description': '',
                    'application_url': link_info['url']
                }
                
                # Try to extract company name
                try:
                    company_selectors = [
                        ".company-name",
                        ".header-company-name", 
                        "[data-qa='company-name']",
                        "h1",
                        ".company"
                    ]
                    for selector in company_selectors:
                        try:
                            company_element = driver.find_element(By.CSS_SELECTOR, selector)
                            company_text = company_element.text.strip()
                            if company_text and len(company_text) < 100:
                                job_details['company'] = company_text
                                break
                        except:
                            continue
                except:
                    pass
                
                # Try to extract location
                try:
                    location_selectors = [
                        ".location",
                        ".job-location",
                        "[data-qa='job-location']",
                        ".office-location"
                    ]
                    for selector in location_selectors:
                        try:
                            location_element = driver.find_element(By.CSS_SELECTOR, selector)
                            location_text = location_element.text.strip()
                            if location_text:
                                job_details['location'] = location_text
                                break
                        except:
                            continue
                except:
                    pass
                
                # Try to extract job description
                try:
                    desc_selectors = [
                        ".job-post-content",
                        ".content",
                        ".description",
                        ".job-description",
                        "main"
                    ]
                    for selector in desc_selectors:
                        try:
                            desc_element = driver.find_element(By.CSS_SELECTOR, selector)
                            desc_text = desc_element.text.strip()
                            if len(desc_text) > 100:  # Only use substantial content
                                job_details['description'] = desc_text[:1000] + "..." if len(desc_text) > 1000 else desc_text
                                break
                        except:
                            continue
                except:
                    pass
                
                jobs.append(job_details)
                
                print(f"   ✅ Company: {job_details['company'] or 'Not found'}")
                print(f"   📍 Location: {job_details['location'] or 'Not found'}")
                print(f"   📝 Description: {len(job_details['description'])} characters")
                
                # Close tab and return to main window
                driver.close()
                driver.switch_to.window(driver.window_handles[0])
                
                # Small delay between requests
                time.sleep(2)
                
            except Exception as e:
                print(f"   ❌ Error visiting link: {e}")
                # Make sure we close any extra tabs
                if len(driver.window_handles) > 1:
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])
                continue
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        return []
        
    finally:
        if driver:
            try:
                print("\n🕐 Keeping browser open for 15 seconds...")
                time.sleep(15)
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def save_results(jobs):
    """Save the extracted job results"""
    if not jobs:
        return
    
    try:
        # Save as JSON
        with open("extracted_jobs.json", 'w', encoding='utf-8') as f:
            json.dump(jobs, f, indent=2, ensure_ascii=False)
        
        # Save as readable text
        with open("extracted_jobs.txt", 'w', encoding='utf-8') as f:
            f.write("EXTRACTED FULL STACK ENGINEER JOBS\n")
            f.write("=" * 50 + "\n\n")
            
            for i, job in enumerate(jobs, 1):
                f.write(f"JOB {i}:\n")
                f.write(f"Title: {job['title']}\n")
                f.write(f"Company: {job['company'] or 'Not specified'}\n")
                f.write(f"Location: {job['location'] or 'Not specified'}\n")
                f.write(f"URL: {job['url']}\n")
                f.write(f"Application URL: {job['application_url']}\n")
                f.write(f"\nDescription:\n{job['description']}\n")
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"💾 Results saved to extracted_jobs.json and extracted_jobs.txt")
        
    except Exception as e:
        print(f"❌ Error saving results: {e}")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 SIMPLE LINK EXTRACTOR AND VISITOR")
    print("🎯 Find and visit each greenhouse.io job link")
    print("=" * 60)
    
    jobs = extract_and_visit_links()
    
    if jobs:
        print(f"\n🎉 SUCCESS! Extracted details from {len(jobs)} jobs!")
        
        print("\n📋 SUMMARY:")
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job['title']}")
            print(f"   🏢 {job['company'] or 'Unknown company'}")
            print(f"   📍 {job['location'] or 'Location not specified'}")
            print(f"   🔗 {job['url']}")
            print()
        
        save_results(jobs)
        
    else:
        print("\n❌ No jobs found or extracted")
        print("💡 This might mean:")
        print("   • No Full Stack Engineer jobs posted in last 24 hours")
        print("   • Google search results don't contain greenhouse.io links")
        print("   • Need to adjust search parameters")

if __name__ == "__main__":
    main()
