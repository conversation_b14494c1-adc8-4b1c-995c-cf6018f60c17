#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time

def read_website_html(url):
    """
    Opens a website in Chrome browser and reads its HTML content
    """
    driver = None
    try:
        print(f"🚀 Starting HTML reader for: {url}")
        
        # Setup Chrome options
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        # Keep browser visible (no headless mode)
        
        # Use the ChromeDriver we installed
        service = Service("/usr/local/bin/chromedriver")
        
        print("🔧 Creating Chrome driver...")
        driver = webdriver.Chrome(service=service, options=options)
        print("✅ Chrome driver created successfully!")
        
        print(f"🌐 Opening: {url}")
        driver.get(url)
        
        # Wait for page to load
        time.sleep(3)
        
        # Get page title
        title = driver.title
        print(f"📄 Page Title: {title}")
        
        # Get full HTML source
        html_content = driver.page_source
        print(f"📝 HTML Length: {len(html_content)} characters")
        
        # Print first 1000 characters of HTML
        print("🔍 HTML Preview (first 1000 chars):")
        print("-" * 60)
        print(html_content[:1000])
        print("-" * 60)
        
        # Find some basic elements
        try:
            # Find all links
            links = driver.find_elements(By.TAG_NAME, "a")
            print(f"🔗 Found {len(links)} links on the page")
            
            # Find all images
            images = driver.find_elements(By.TAG_NAME, "img")
            print(f"🖼️ Found {len(images)} images on the page")
            
            # Find all buttons
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"🔘 Found {len(buttons)} buttons on the page")
            
            # Print first 3 links
            if links:
                print("📋 First 3 links:")
                for i, link in enumerate(links[:3]):
                    link_text = link.text.strip()
                    link_url = link.get_attribute("href")
                    print(f"  {i+1}. Text: '{link_text}' -> URL: {link_url}")
                    
        except Exception as e:
            print(f"⚠️ Error finding elements: {e}")
        
        # Keep browser open so you can see it
        print("🕐 Keeping browser open for 20 seconds so you can see it...")
        print("💡 You can manually close the browser window or wait for auto-close")
        time.sleep(20)
        
        print("✅ Successfully read HTML!")
        return html_content
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def main():
    """
    Main function - you can change the URL here
    """
    # Change this URL to whatever website you want to read
    url = "https://www.workatastartup.com/companies"
    
    print("=" * 60)
    print("🌐 SELENIUM HTML READER")
    print("=" * 60)
    
    html_content = read_website_html(url)
    
    if html_content:
        print(f"✅ Success! Read {len(html_content)} characters from {url}")
        
        # Optionally save HTML to file
        save_to_file = input("\n💾 Save HTML to file? (y/n): ").lower().strip()
        if save_to_file == 'y':
            filename = f"html_content_{int(time.time())}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"💾 HTML saved to: {filename}")
    else:
        print("❌ Failed to read HTML")

if __name__ == "__main__":
    main()
