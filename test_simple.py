#!/usr/bin/env python3

print("🚀 Starting simple Selenium test...")

try:
    from selenium import webdriver
    print("✅ Selenium imported successfully")
    
    # Try to create a simple Chrome driver
    from selenium.webdriver.chrome.service import Service

    options = webdriver.ChromeOptions()
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")

    # Use the ChromeDriver we just installed
    service = Service("/usr/local/bin/chromedriver")

    print("🔧 Creating Chrome driver...")
    driver = webdriver.Chrome(service=service, options=options)
    print("✅ Chrome driver created successfully!")
    
    print("🌐 Opening Google...")
    driver.get("https://www.google.com")
    
    print(f"📄 Page title: {driver.title}")
    print("✅ Success! Browser should be open now.")
    
    # Keep browser open for 5 seconds
    import time
    print("⏰ Waiting 5 seconds...")
    time.sleep(5)
    
    driver.quit()
    print("🧹 Browser closed.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("💡 Make sure Chrome is installed and accessible")
