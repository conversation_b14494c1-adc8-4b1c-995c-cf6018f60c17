#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time
import random

def setup_stealth_driver():
    """Set up Chrome driver with stealth options"""
    options = webdriver.ChromeOptions()
    
    # Stealth options
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    service = Service("/usr/local/bin/chromedriver")
    driver = webdriver.Chrome(service=service, options=options)
    
    # Hide webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def debug_search_results():
    """
    Debug what's actually in the Google search results
    """
    driver = None
    
    try:
        print("🔍 DEBUG: ANALYZING GOOGLE SEARCH RESULTS")
        print("=" * 60)
        
        driver = setup_stealth_driver()
        print("✅ Chrome driver created!")
        
        # Visit Google homepage first
        print("🌐 Visiting Google homepage...")
        driver.get("https://www.google.com")
        time.sleep(3)
        
        # Navigate to search results
        search_url = "https://www.google.com/search?q=%22Full%20Stack%20Engineer%22+site:greenhouse.io+remote&tbs=qdr:d"
        print("🔍 Loading search results...")
        driver.get(search_url)
        time.sleep(5)
        
        # Check page title and basic info
        print(f"📄 Page title: {driver.title}")
        print(f"🔗 Current URL: {driver.current_url}")
        
        # Check if blocked
        page_text = driver.find_element(By.TAG_NAME, "body").text
        if "unusual traffic" in page_text.lower() or "captcha" in page_text.lower():
            print("🚫 BLOCKED: Google is showing CAPTCHA")
            print(f"Page content: {page_text[:500]}...")
            return
        
        print("✅ Not blocked - analyzing results...")
        
        # Try different selectors and see what we get
        selectors_to_debug = [
            ("div.g", "Traditional Google results"),
            ("div[data-ved]", "Data-ved elements"),
            (".g", "Simple .g class"),
            ("div.yuRUbf", "yuRUbf containers"),
            ("div.tF2Cxc", "tF2Cxc containers"),
            ("h3", "All H3 elements"),
            ("a[href]", "All links"),
        ]
        
        for selector, description in selectors_to_debug:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                print(f"\n🔍 {description} ({selector}): Found {len(elements)} elements")
                
                # Show first few elements
                for i, element in enumerate(elements[:3]):
                    try:
                        text = element.text.strip()[:100]
                        if selector == "a[href]":
                            href = element.get_attribute("href")
                            print(f"   {i+1}. Text: '{text}' -> URL: {href}")
                        else:
                            print(f"   {i+1}. Text: '{text}'")
                    except Exception as e:
                        print(f"   {i+1}. Error getting text: {e}")
                        
            except Exception as e:
                print(f"❌ Error with {selector}: {e}")
        
        # Look specifically for greenhouse.io links
        print(f"\n🏢 Looking specifically for greenhouse.io links...")
        all_links = driver.find_elements(By.CSS_SELECTOR, "a[href]")
        greenhouse_links = []
        
        for link in all_links:
            try:
                href = link.get_attribute("href")
                if href and "greenhouse.io" in href.lower():
                    text = link.text.strip()
                    greenhouse_links.append((text, href))
            except:
                continue
        
        print(f"🎯 Found {len(greenhouse_links)} greenhouse.io links:")
        for i, (text, href) in enumerate(greenhouse_links[:10], 1):
            print(f"   {i}. '{text}' -> {href}")
        
        # Save page source for manual inspection
        try:
            with open("debug_page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print(f"\n💾 Page source saved to debug_page_source.html")
        except Exception as e:
            print(f"❌ Error saving page source: {e}")
        
        # Keep browser open for manual inspection
        print(f"\n🕐 Keeping browser open for 30 seconds for manual inspection...")
        print("💡 You can manually check the search results in the browser")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🧹 Browser closed.")
            except:
                print("🧹 Browser was already closed.")

def main():
    """Main function"""
    print("=" * 70)
    print("🐛 DEBUG GOOGLE SEARCH RESULTS")
    print("=" * 70)
    
    debug_search_results()

if __name__ == "__main__":
    main()
