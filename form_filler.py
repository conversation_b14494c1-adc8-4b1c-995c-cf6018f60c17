#!/usr/bin/env python3

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import time
import json
import os
from typing import Dict, List

class JobApplicationFormFiller:
    """Automated job application form filler with sample data"""
    
    def __init__(self):
        self.driver = None
        self.setup_driver()
        self.sample_data = self.load_sample_data()
    
    def setup_driver(self):
        """Set up Chrome driver with stealth options"""
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        service = Service("/usr/local/bin/chromedriver")
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    def load_sample_data(self) -> Dict:
        """Load sample application data"""
        return {
            'personal': {
                'first_name': 'John',
                'last_name': 'Smith',
                'full_name': 'John Smith',
                'email': '<EMAIL>',
                'phone': '+****************',
                'address': '123 Tech Street',
                'city': 'San Francisco',
                'state': 'California',
                'zip_code': '94105',
                'country': 'United States',
                'linkedin': 'https://linkedin.com/in/johnsmith-dev',
                'github': 'https://github.com/johnsmith-dev',
                'portfolio': 'https://johnsmith.dev'
            },
            'professional': {
                'current_title': 'Senior Full Stack Engineer',
                'current_company': 'Tech Innovations Inc.',
                'years_experience': '5',
                'salary_expectation': '150000',
                'availability': 'Immediately',
                'work_authorization': 'Yes',
                'remote_work': 'Yes',
                'relocation': 'No'
            },
            'education': {
                'degree': 'Bachelor of Science in Computer Science',
                'university': 'University of California, Berkeley',
                'graduation_year': '2019'
            },
            'cover_letter': """Dear Hiring Manager,

I am excited to apply for the Full Stack Engineer position. With 5+ years of experience in modern web development, I have expertise in React, Node.js, Python, and cloud technologies.

In my current role at Tech Innovations Inc., I have:
• Led development of scalable web applications serving 100K+ users
• Implemented CI/CD pipelines reducing deployment time by 60%
• Mentored junior developers and contributed to technical architecture decisions

I am passionate about building user-centric applications and would love to contribute to your team's success.

Best regards,
John Smith""",
            'files': {
                'resume_path': '/Users/<USER>/Desktop/semeon-apply/sample_resume.pdf',
                'cover_letter_path': '/Users/<USER>/Desktop/semeon-apply/sample_cover_letter.pdf'
            }
        }
    
    def handle_page_obstacles(self):
        """Handle cookies, popups, and other obstacles"""
        print("🍪 Handling page obstacles...")
        
        # Cookie and consent selectors
        obstacle_selectors = [
            "#onetrust-accept-btn-handler",
            ".optanon-allow-all",
            "button[class*='accept']",
            "button[id*='accept']",
            "[aria-label*='accept'] button",
            "button:contains('Accept')",
            "button:contains('Allow')",
            "button:contains('OK')",
            "button:contains('Continue')",
            "[class*='close']",
            ".modal-close"
        ]
        
        for selector in obstacle_selectors:
            try:
                script = f"""
                var elements = document.querySelectorAll('{selector}');
                for (var i = 0; i < elements.length; i++) {{
                    var el = elements[i];
                    if (el.offsetParent !== null) {{
                        el.click();
                        return true;
                    }}
                }}
                return false;
                """
                
                clicked = self.driver.execute_script(script)
                if clicked:
                    print(f"   ✅ Handled obstacle: {selector}")
                    time.sleep(1)
                    break
            except:
                continue
    
    def scroll_to_element(self, element):
        """Scroll element into view"""
        try:
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
            time.sleep(0.5)
        except:
            pass
    
    def fill_text_field(self, element, value: str, field_name: str = ""):
        """Fill a text field with proper handling"""
        try:
            self.scroll_to_element(element)
            
            # Clear existing content
            element.clear()
            time.sleep(0.2)
            
            # Type the value
            element.send_keys(value)
            time.sleep(0.3)
            
            print(f"   ✅ Filled {field_name}: {value}")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to fill {field_name}: {e}")
            return False
    
    def select_dropdown_option(self, element, value: str, field_name: str = ""):
        """Select dropdown option"""
        try:
            self.scroll_to_element(element)
            
            select = Select(element)
            
            # Try different selection methods
            try:
                select.select_by_visible_text(value)
            except:
                try:
                    select.select_by_value(value)
                except:
                    # Select first option if exact match fails
                    options = select.options
                    if len(options) > 1:
                        select.select_by_index(1)
            
            print(f"   ✅ Selected {field_name}: {value}")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to select {field_name}: {e}")
            return False
    
    def upload_file(self, element, file_path: str, field_name: str = ""):
        """Upload a file"""
        try:
            if not os.path.exists(file_path):
                print(f"   ⚠️ File not found: {file_path}")
                return False
            
            self.scroll_to_element(element)
            element.send_keys(file_path)
            time.sleep(1)
            
            print(f"   ✅ Uploaded {field_name}: {os.path.basename(file_path)}")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to upload {field_name}: {e}")
            return False
    
    def identify_field_type(self, element) -> str:
        """Identify what type of field this is based on attributes"""
        try:
            # Get element attributes
            name = (element.get_attribute('name') or '').lower()
            id_attr = (element.get_attribute('id') or '').lower()
            placeholder = (element.get_attribute('placeholder') or '').lower()
            label_text = ''
            
            # Try to find associated label
            try:
                if id_attr:
                    label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{id_attr}']")
                    label_text = label.text.lower()
            except:
                pass
            
            # Combine all text for analysis
            all_text = f"{name} {id_attr} {placeholder} {label_text}"
            
            # Field type mapping
            field_mappings = {
                'first_name': ['first', 'fname', 'firstname', 'given'],
                'last_name': ['last', 'lname', 'lastname', 'family', 'surname'],
                'full_name': ['name', 'fullname', 'full_name'],
                'email': ['email', 'mail'],
                'phone': ['phone', 'tel', 'mobile', 'number'],
                'address': ['address', 'street'],
                'city': ['city', 'town'],
                'state': ['state', 'province', 'region'],
                'zip_code': ['zip', 'postal', 'postcode'],
                'country': ['country', 'nation'],
                'linkedin': ['linkedin'],
                'github': ['github'],
                'portfolio': ['portfolio', 'website', 'url'],
                'current_title': ['title', 'position', 'role', 'job'],
                'current_company': ['company', 'employer', 'organization'],
                'years_experience': ['experience', 'years'],
                'salary_expectation': ['salary', 'compensation', 'pay'],
                'cover_letter': ['cover', 'letter', 'message', 'why', 'interest']
            }
            
            # Find best match
            for field_type, keywords in field_mappings.items():
                if any(keyword in all_text for keyword in keywords):
                    return field_type
            
            return 'unknown'
            
        except Exception as e:
            return 'unknown'
    
    def fill_form_on_page(self, url: str):
        """Fill out the application form on a job page"""
        try:
            print(f"🔍 Opening job page: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            # Handle obstacles
            self.handle_page_obstacles()
            
            # Scroll to load content
            print("📜 Scrolling to load all content...")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # Find all forms
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            print(f"📝 Found {len(forms)} forms on page")
            
            if not forms:
                print("❌ No forms found on page")
                return False
            
            # Process each form
            for form_index, form in enumerate(forms):
                print(f"\n📋 Processing Form {form_index + 1}:")
                
                # Find all input fields in this form
                inputs = form.find_elements(By.TAG_NAME, "input")
                textareas = form.find_elements(By.TAG_NAME, "textarea")
                selects = form.find_elements(By.TAG_NAME, "select")
                
                all_fields = inputs + textareas + selects
                print(f"   Found {len(all_fields)} fields")
                
                # Fill each field
                for field in all_fields:
                    try:
                        field_type = self.identify_field_type(field)
                        input_type = field.get_attribute('type') or 'text'
                        
                        if input_type == 'file':
                            # Handle file upload
                            if 'resume' in field_type or 'cv' in str(field.get_attribute('name')).lower():
                                self.upload_file(field, self.sample_data['files']['resume_path'], 'Resume')
                            else:
                                self.upload_file(field, self.sample_data['files']['cover_letter_path'], 'Cover Letter')
                        
                        elif field.tag_name == 'select':
                            # Handle dropdown
                            if field_type in self.sample_data['personal']:
                                value = self.sample_data['personal'][field_type]
                            elif field_type in self.sample_data['professional']:
                                value = self.sample_data['professional'][field_type]
                            else:
                                value = "Yes"  # Default for yes/no questions
                            
                            self.select_dropdown_option(field, value, field_type)
                        
                        elif field.tag_name == 'textarea':
                            # Handle textarea (usually cover letter)
                            if 'cover' in field_type or 'message' in field_type:
                                self.fill_text_field(field, self.sample_data['cover_letter'], 'Cover Letter')
                            else:
                                self.fill_text_field(field, "I am interested in this position and believe I would be a great fit.", field_type)
                        
                        elif input_type in ['text', 'email', 'tel', 'url']:
                            # Handle text inputs
                            if field_type in self.sample_data['personal']:
                                value = self.sample_data['personal'][field_type]
                            elif field_type in self.sample_data['professional']:
                                value = self.sample_data['professional'][field_type]
                            elif field_type in self.sample_data['education']:
                                value = self.sample_data['education'][field_type]
                            else:
                                value = "N/A"
                            
                            self.fill_text_field(field, value, field_type)
                        
                        # Small delay between fields
                        time.sleep(0.5)
                        
                    except Exception as e:
                        print(f"   ⚠️ Error processing field: {e}")
                        continue
            
            print(f"\n✅ Form filling completed!")
            print("🎯 Ready for manual review and submission")
            
            # Keep browser open for manual review
            print("\n⏳ Keeping browser open for 60 seconds for review...")
            print("💡 You can manually review the filled form and submit if everything looks good")
            time.sleep(60)
            
            return True
            
        except Exception as e:
            print(f"❌ Error filling form: {e}")
            return False
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()

def create_sample_files():
    """Create sample resume and cover letter files if they don't exist"""
    resume_content = """JOHN SMITH
Senior Full Stack Engineer
Email: <EMAIL> | Phone: +****************
LinkedIn: linkedin.com/in/johnsmith-dev | GitHub: github.com/johnsmith-dev

EXPERIENCE
Senior Full Stack Engineer | Tech Innovations Inc. | 2021 - Present
• Led development of scalable web applications serving 100K+ users
• Implemented CI/CD pipelines reducing deployment time by 60%
• Technologies: React, Node.js, Python, AWS, Docker

Full Stack Developer | StartupCorp | 2019 - 2021
• Built responsive web applications using modern frameworks
• Collaborated with cross-functional teams in agile environment
• Technologies: Vue.js, Express.js, PostgreSQL, MongoDB

EDUCATION
Bachelor of Science in Computer Science
University of California, Berkeley | 2019

SKILLS
Frontend: React, Vue.js, TypeScript, HTML5, CSS3
Backend: Node.js, Python, Express.js, Django
Database: PostgreSQL, MongoDB, Redis
Cloud: AWS, Docker, Kubernetes
"""
    
    # Save as text file (since we can't create actual PDFs easily)
    with open('/Users/<USER>/Desktop/semeon-apply/sample_resume.txt', 'w') as f:
        f.write(resume_content)
    
    print("📄 Created sample resume file: sample_resume.txt")

def main():
    """Main function to test form filling"""
    # Create sample files
    create_sample_files()
    
    # Test URLs (the 5 job URLs we found)
    test_urls = [
        "https://boards.greenhouse.io/waymo/jobs/6508003",
        "https://boards.greenhouse.io/coveoen/jobs/7944459002", 
        "https://boards.greenhouse.io/waymo/jobs/6885682",
        "http://job-boards.greenhouse.io/nerdy/jobs/7045082",
        "https://boards.greenhouse.io/squarespace/jobs/7036031"
    ]
    
    print("🤖 JOB APPLICATION FORM FILLER")
    print("=" * 50)
    print("🎯 This will automatically fill job application forms with sample data")
    print("⚠️  Please review all filled information before submitting!")
    print()
    
    # Ask user which job to apply to
    print("Available jobs:")
    for i, url in enumerate(test_urls, 1):
        print(f"{i}. {url}")
    
    try:
        choice = input(f"\nEnter job number (1-{len(test_urls)}) or 'all' for all jobs: ").strip()
        
        filler = JobApplicationFormFiller()
        
        if choice.lower() == 'all':
            for i, url in enumerate(test_urls, 1):
                print(f"\n🎯 FILLING APPLICATION {i}/{len(test_urls)}")
                print("=" * 60)
                filler.fill_form_on_page(url)
                
                if i < len(test_urls):
                    input("Press Enter to continue to next application...")
        else:
            job_index = int(choice) - 1
            if 0 <= job_index < len(test_urls):
                url = test_urls[job_index]
                print(f"\n🎯 FILLING APPLICATION FOR: {url}")
                print("=" * 60)
                filler.fill_form_on_page(url)
            else:
                print("❌ Invalid job number")
        
        filler.close()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
